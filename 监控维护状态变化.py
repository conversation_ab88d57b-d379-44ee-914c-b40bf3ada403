#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
监控维护状态变化脚本
实时监控维护状态的变化，找出自动触发维护的原因
"""

import os
import sys
import django
import time
import datetime

# 添加项目路径
sys.path.append('server/trunk/llol')
sys.path.append('server/trunk/game_lib')

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'llol.settings')
django.setup()

from game_lib.models.main import CacheTable

class MaintainMonitor:
    def __init__(self):
        self.last_full_status = None
        self.last_zone_status = None
        self.last_config = None
        self.log_file = f"maintain_monitor_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        
        # 写入文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_msg + '\n')
    
    def get_current_status(self):
        """获取当前维护状态"""
        try:
            full_maintain_status = CacheTable.get('full_maintain_status', 1)
            zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
            maintain_config = CacheTable.get('zone_maintain_config', '{}')
            maintain_config = eval(str(maintain_config))
            
            return {
                'full_maintain': full_maintain_status,
                'zone_maintain': zone_maintain_status,
                'config': maintain_config,
                'timestamp': datetime.datetime.now()
            }
        except Exception as e:
            self.log(f"❌ 获取状态失败: {e}")
            return None
    
    def check_changes(self, current_status):
        """检查状态变化"""
        if not current_status:
            return
            
        changes = []
        
        # 检查全服维护状态变化
        if self.last_full_status != current_status['full_maintain']:
            old_status = "维护中" if self.last_full_status == 0 else "正常" if self.last_full_status == 1 else "未知"
            new_status = "维护中" if current_status['full_maintain'] == 0 else "正常"
            changes.append(f"全服维护状态: {old_status} → {new_status}")
            self.last_full_status = current_status['full_maintain']
        
        # 检查分区维护状态变化
        if self.last_zone_status != current_status['zone_maintain']:
            old_status = "维护中" if self.last_zone_status == 0 else "正常" if self.last_zone_status == 1 else "未知"
            new_status = "维护中" if current_status['zone_maintain'] == 0 else "正常"
            changes.append(f"分区维护状态: {old_status} → {new_status}")
            self.last_zone_status = current_status['zone_maintain']
        
        # 检查配置变化
        if self.last_config != current_status['config']:
            changes.append(f"维护配置变化: {self.last_config} → {current_status['config']}")
            self.last_config = current_status['config']
        
        # 记录变化
        if changes:
            self.log("🚨 检测到维护状态变化:")
            for change in changes:
                self.log(f"   - {change}")
            
            # 记录详细信息
            self.log(f"   详细状态: {current_status}")
            
            # 如果变为维护状态，记录可能的原因
            if current_status['full_maintain'] == 0 or current_status['zone_maintain'] == 0:
                self.log("⚠️  可能的触发原因:")
                self.log("   1. 管理后台手动设置")
                self.log("   2. 服务器管理脚本自动触发")
                self.log("   3. 内存不足自动保护")
                self.log("   4. 备份失败自动维护")
                self.log("   5. 缓存服务异常")
    
    def check_related_cache_keys(self):
        """检查相关的缓存键"""
        try:
            related_keys = [
                'server_manage_config',
                'config_update_time',
                'notice_update_time',
                'zone_maintain_disp',
            ]
            
            self.log("\n📋 相关缓存状态:")
            for key in related_keys:
                try:
                    value = CacheTable.get(key, None)
                    self.log(f"   {key}: {value}")
                except Exception as e:
                    self.log(f"   {key}: 获取失败 - {e}")
                    
        except Exception as e:
            self.log(f"❌ 检查缓存失败: {e}")
    
    def monitor(self, interval=5, duration=3600):
        """开始监控"""
        self.log("🔍 开始监控维护状态变化...")
        self.log(f"   监控间隔: {interval}秒")
        self.log(f"   监控时长: {duration}秒")
        self.log(f"   日志文件: {self.log_file}")
        
        # 初始状态
        initial_status = self.get_current_status()
        if initial_status:
            self.log(f"📊 初始状态: {initial_status}")
            self.last_full_status = initial_status['full_maintain']
            self.last_zone_status = initial_status['zone_maintain']
            self.last_config = initial_status['config']
        
        # 检查相关缓存
        self.check_related_cache_keys()
        
        start_time = time.time()
        check_count = 0
        
        try:
            while time.time() - start_time < duration:
                current_status = self.get_current_status()
                if current_status:
                    self.check_changes(current_status)
                
                check_count += 1
                if check_count % 60 == 0:  # 每5分钟输出一次状态
                    self.log(f"✅ 监控正常 - 已检查 {check_count} 次")
                
                time.sleep(interval)
                
        except KeyboardInterrupt:
            self.log("⏹️  监控被用户中断")
        except Exception as e:
            self.log(f"❌ 监控过程中出现错误: {e}")
        
        self.log(f"🏁 监控结束 - 总共检查 {check_count} 次")

def main():
    print("=" * 60)
    print("游戏维护状态监控工具")
    print("=" * 60)
    print("此工具将实时监控维护状态变化，帮助找出自动触发维护的原因")
    print()
    
    monitor = MaintainMonitor()
    
    # 获取监控参数
    try:
        interval = int(input("请输入监控间隔(秒，默认5): ") or "5")
        duration = int(input("请输入监控时长(秒，默认3600): ") or "3600")
    except ValueError:
        interval = 5
        duration = 3600
    
    print(f"\n开始监控，间隔{interval}秒，持续{duration}秒...")
    print("按 Ctrl+C 可以提前结束监控")
    print("=" * 60)
    
    monitor.monitor(interval, duration)

if __name__ == "__main__":
    main()
