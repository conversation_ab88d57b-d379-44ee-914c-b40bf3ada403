#!/bin/bash
# 攻击监控和自动防护脚本

LOG_FILE="/var/log/nginx/error.log"
ACCESS_LOG="/var/log/nginx/access.log"
BLOCK_LIST="/tmp/blocked_ips.txt"
ALERT_LOG="attack_alerts_$(date +%Y%m%d).log"

log_alert() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$ALERT_LOG"
}

# 检测扫描攻击
detect_scan_attacks() {
    log_alert "开始检测扫描攻击..."
    
    # 检测常见扫描特征
    local scan_patterns="nmaplowercheck|evox|HNAP1|sdk|admin|phpmyadmin|wp-admin"
    
    # 获取最近5分钟的攻击IP
    local attack_ips=$(grep "$(date -d '5 minutes ago' '+%Y/%m/%d %H:%M')" "$LOG_FILE" 2>/dev/null | \
                      grep -E "$scan_patterns" | \
                      awk '{print $12}' | \
                      sed 's/client://' | sed 's/,//' | \
                      sort | uniq -c | \
                      awk '$1 >= 3 {print $2}')
    
    for ip in $attack_ips; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            block_ip "$ip" "扫描攻击"
        fi
    done
}

# 检测高频访问
detect_high_frequency() {
    log_alert "检测高频访问..."
    
    # 获取最近1分钟访问超过100次的IP
    local high_freq_ips=$(tail -1000 "$ACCESS_LOG" 2>/dev/null | \
                         awk -v cutoff="$(date -d '1 minute ago' '+%d/%b/%Y:%H:%M')" \
                         '$4 > "["cutoff {print $1}' | \
                         sort | uniq -c | \
                         awk '$1 > 100 {print $2}')
    
    for ip in $high_freq_ips; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            block_ip "$ip" "高频访问"
        fi
    done
}

# 封禁IP
block_ip() {
    local ip=$1
    local reason=$2
    
    # 检查是否已经封禁
    if iptables -L INPUT -n | grep -q "$ip"; then
        return
    fi
    
    # 检查是否是内网IP
    if [[ $ip =~ ^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.|127\.) ]]; then
        log_alert "跳过内网IP: $ip"
        return
    fi
    
    log_alert "🚨 封禁IP: $ip (原因: $reason)"
    
    # 添加iptables规则
    iptables -A INPUT -s "$ip" -j DROP
    
    # 记录到封禁列表
    echo "$(date '+%Y-%m-%d %H:%M:%S') $ip $reason" >> "$BLOCK_LIST"
    
    # 发送告警（可选）
    # echo "服务器检测到攻击IP: $ip，已自动封禁" | mail -s "安全告警" <EMAIL>
}

# 清理过期封禁
cleanup_old_blocks() {
    log_alert "清理24小时前的封禁规则..."
    
    if [ -f "$BLOCK_LIST" ]; then
        # 获取24小时前的时间戳
        local cutoff=$(date -d '24 hours ago' '+%Y-%m-%d %H:%M:%S')
        
        # 读取需要解封的IP
        while read -r line; do
            local block_time=$(echo "$line" | awk '{print $1" "$2}')
            local ip=$(echo "$line" | awk '{print $3}')
            
            if [[ "$block_time" < "$cutoff" ]]; then
                log_alert "解封过期IP: $ip"
                iptables -D INPUT -s "$ip" -j DROP 2>/dev/null
            fi
        done < "$BLOCK_LIST"
        
        # 更新封禁列表
        grep -v "$cutoff" "$BLOCK_LIST" > "${BLOCK_LIST}.tmp" 2>/dev/null
        mv "${BLOCK_LIST}.tmp" "$BLOCK_LIST" 2>/dev/null
    fi
}

# 生成安全报告
generate_report() {
    log_alert "========== 安全监控报告 =========="
    log_alert "当前封禁IP数量: $(iptables -L INPUT -n | grep DROP | wc -l)"
    log_alert "今日攻击次数: $(grep "$(date '+%Y/%m/%d')" "$LOG_FILE" 2>/dev/null | grep -E "nmaplowercheck|evox|HNAP1" | wc -l)"
    log_alert "当前连接数: $(ss -tn | wc -l)"
    log_alert "系统负载: $(uptime | awk -F'load average:' '{print $2}')"
}

# 主监控循环
main_monitor() {
    log_alert "启动攻击监控系统..."
    
    while true; do
        detect_scan_attacks
        detect_high_frequency
        
        # 每小时清理一次过期封禁
        if [ $(($(date +%M) % 60)) -eq 0 ]; then
            cleanup_old_blocks
            generate_report
        fi
        
        sleep 60  # 每分钟检查一次
    done
}

# 手动封禁IP
manual_block() {
    if [ -z "$1" ]; then
        echo "用法: $0 block <IP地址>"
        exit 1
    fi
    
    block_ip "$1" "手动封禁"
}

# 查看当前封禁列表
show_blocked() {
    echo "当前封禁的IP列表:"
    iptables -L INPUT -n | grep DROP | awk '{print $4}' | grep -v "0.0.0.0/0"
    
    if [ -f "$BLOCK_LIST" ]; then
        echo -e "\n详细封禁记录:"
        cat "$BLOCK_LIST"
    fi
}

# 命令行参数处理
case "$1" in
    "monitor")
        main_monitor
        ;;
    "block")
        manual_block "$2"
        ;;
    "show")
        show_blocked
        ;;
    "report")
        generate_report
        ;;
    *)
        echo "用法: $0 {monitor|block <IP>|show|report}"
        echo "  monitor - 启动监控"
        echo "  block   - 手动封禁IP"
        echo "  show    - 显示封禁列表"
        echo "  report  - 生成安全报告"
        exit 1
        ;;
esac
