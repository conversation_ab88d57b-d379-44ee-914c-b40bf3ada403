@echo off
chcp 65001
echo ==========================================
echo           MySQL数据库备份工具
echo ==========================================
echo.

:: 设置变量（请根据实际情况修改）
set MYSQL_USER=root
set MYSQL_HOST=localhost
set MYSQL_PORT=3306

:: 创建备份目录
set BACKUP_DIR=database_backup_%date:~0,4%%date:~5,2%%date:~8,2%
if not exist "%BACKUP_DIR%" mkdir "%BACKUP_DIR%"

echo 请输入MySQL密码:
set /p MYSQL_PASSWORD=

echo.
echo 选择备份方式:
echo 1. 备份单个数据库
echo 2. 备份所有数据库
echo 3. 备份指定表
echo 4. 查看所有数据库列表
set /p CHOICE=请输入选择 (1-4): 

if "%CHOICE%"=="1" goto SINGLE_DB
if "%CHOICE%"=="2" goto ALL_DB
if "%CHOICE%"=="3" goto SPECIFIC_TABLES
if "%CHOICE%"=="4" goto LIST_DB

:LIST_DB
echo.
echo 正在获取数据库列表...
mysql -u%MYSQL_USER% -p%MYSQL_PASSWORD% -h%MYSQL_HOST% -P%MYSQL_PORT% -e "SHOW DATABASES;"
echo.
pause
goto START

:SINGLE_DB
echo.
set /p DB_NAME=请输入要备份的数据库名: 
echo 正在备份数据库: %DB_NAME%
set BACKUP_FILE=%BACKUP_DIR%\%DB_NAME%_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql

mysqldump -u%MYSQL_USER% -p%MYSQL_PASSWORD% -h%MYSQL_HOST% -P%MYSQL_PORT% --routines --triggers --single-transaction %DB_NAME% > "%BACKUP_FILE%"

if %ERRORLEVEL% EQU 0 (
    echo ✅ 备份成功！
    echo 备份文件: %BACKUP_FILE%
) else (
    echo ❌ 备份失败！
)
goto END

:ALL_DB
echo.
echo 正在备份所有数据库...
set BACKUP_FILE=%BACKUP_DIR%\all_databases_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql

mysqldump -u%MYSQL_USER% -p%MYSQL_PASSWORD% -h%MYSQL_HOST% -P%MYSQL_PORT% --routines --triggers --single-transaction --all-databases > "%BACKUP_FILE%"

if %ERRORLEVEL% EQU 0 (
    echo ✅ 备份成功！
    echo 备份文件: %BACKUP_FILE%
) else (
    echo ❌ 备份失败！
)
goto END

:SPECIFIC_TABLES
echo.
set /p DB_NAME=请输入数据库名: 
set /p TABLE_NAMES=请输入表名（多个表用空格分隔）: 
echo 正在备份指定表...
set BACKUP_FILE=%BACKUP_DIR%\%DB_NAME%_tables_backup_%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%.sql

mysqldump -u%MYSQL_USER% -p%MYSQL_PASSWORD% -h%MYSQL_HOST% -P%MYSQL_PORT% --routines --triggers --single-transaction %DB_NAME% %TABLE_NAMES% > "%BACKUP_FILE%"

if %ERRORLEVEL% EQU 0 (
    echo ✅ 备份成功！
    echo 备份文件: %BACKUP_FILE%
) else (
    echo ❌ 备份失败！
)
goto END

:END
echo.
echo 备份完成！文件保存在: %BACKUP_DIR%
echo.

:: 询问是否压缩
set /p COMPRESS=是否压缩备份文件？(y/n): 
if /i "%COMPRESS%"=="y" (
    echo 正在压缩备份文件...
    powershell Compress-Archive -Path "%BACKUP_DIR%\*.sql" -DestinationPath "%BACKUP_DIR%_compressed.zip"
    echo ✅ 压缩完成: %BACKUP_DIR%_compressed.zip
)

echo.
echo 备份任务完成！
pause
