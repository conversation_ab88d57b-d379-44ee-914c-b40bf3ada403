#!/bin/bash
# 服务器问题全面诊断脚本

echo "=========================================="
echo "        服务器问题全面诊断工具"
echo "=========================================="

LOG_FILE="server_diagnosis_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log "开始服务器诊断..."

# 1. 系统资源检查
log "==================== 系统资源检查 ===================="
log "内存使用情况:"
free -h | tee -a "$LOG_FILE"

log "磁盘使用情况:"
df -h | tee -a "$LOG_FILE"

log "CPU使用情况:"
top -bn1 | head -20 | tee -a "$LOG_FILE"

log "系统负载:"
uptime | tee -a "$LOG_FILE"

# 2. 进程检查
log "==================== 进程检查 ===================="
log "Python游戏进程:"
ps aux | grep python | grep -v grep | tee -a "$LOG_FILE"

log "MySQL进程:"
ps aux | grep mysql | grep -v grep | tee -a "$LOG_FILE"

log "端口占用情况:"
netstat -tlnp | grep -E ":(8001|8002|8003|8004|8005|3306)" | tee -a "$LOG_FILE"

# 3. 日志检查
log "==================== 日志检查 ===================="
log "系统日志错误 (最近100行):"
tail -100 /var/log/messages 2>/dev/null | grep -i error | tee -a "$LOG_FILE"

log "MySQL错误日志:"
if [ -f /var/log/mysql/error.log ]; then
    tail -50 /var/log/mysql/error.log | tee -a "$LOG_FILE"
elif [ -f /var/log/mysqld.log ]; then
    tail -50 /var/log/mysqld.log | tee -a "$LOG_FILE"
fi

log "游戏服务器日志 (如果存在):"
find /data -name "*.log" -mtime -1 -exec echo "=== {} ===" \; -exec tail -20 {} \; 2>/dev/null | tee -a "$LOG_FILE"

# 4. 数据库连接检查
log "==================== 数据库检查 ===================="
log "MySQL连接数:"
mysql -uroot -p -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null | tee -a "$LOG_FILE"

log "MySQL最大连接数:"
mysql -uroot -p -e "SHOW VARIABLES LIKE 'max_connections';" 2>/dev/null | tee -a "$LOG_FILE"

log "MySQL进程列表:"
mysql -uroot -p -e "SHOW PROCESSLIST;" 2>/dev/null | tee -a "$LOG_FILE"

# 5. 网络检查
log "==================== 网络检查 ===================="
log "网络连接状态:"
ss -tuln | tee -a "$LOG_FILE"

log "网络连接数统计:"
ss -s | tee -a "$LOG_FILE"

# 6. 文件系统检查
log "==================== 文件系统检查 ===================="
log "inode使用情况:"
df -i | tee -a "$LOG_FILE"

log "最近修改的重要文件:"
find /data -name "*.py" -o -name "*.conf" -o -name "*.cfg" | xargs ls -lt | head -20 | tee -a "$LOG_FILE"

# 7. 定时任务检查
log "==================== 定时任务检查 ===================="
log "root用户的crontab:"
crontab -l 2>/dev/null | tee -a "$LOG_FILE"

log "系统定时任务:"
ls -la /etc/cron.* | tee -a "$LOG_FILE"

# 8. 内核消息检查
log "==================== 内核消息检查 ===================="
log "OOM Killer消息 (内存不足杀进程):"
dmesg | grep -i "killed process" | tail -10 | tee -a "$LOG_FILE"

log "内存相关错误:"
dmesg | grep -i "memory" | tail -10 | tee -a "$LOG_FILE"

# 9. 服务状态检查
log "==================== 服务状态检查 ===================="
log "MySQL服务状态:"
systemctl status mysql 2>/dev/null || systemctl status mysqld 2>/dev/null | tee -a "$LOG_FILE"

# 10. 生成建议
log "==================== 诊断建议 ===================="

# 检查内存使用率
MEM_USAGE=$(free | grep Mem | awk '{printf("%.1f"), $3/$2 * 100.0}')
if (( $(echo "$MEM_USAGE > 80" | bc -l) )); then
    log "⚠️  内存使用率过高: ${MEM_USAGE}%"
    log "建议: 增加内存或优化程序"
fi

# 检查磁盘使用率
DISK_USAGE=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
if [ "$DISK_USAGE" -gt 80 ]; then
    log "⚠️  磁盘使用率过高: ${DISK_USAGE}%"
    log "建议: 清理磁盘空间"
fi

# 检查进程数量
PYTHON_PROCESSES=$(ps aux | grep python | grep -v grep | wc -l)
log "Python进程数量: $PYTHON_PROCESSES"

log "==================== 诊断完成 ===================="
log "诊断报告已保存到: $LOG_FILE"
log "请将此报告发送给技术支持人员进行分析"

echo
echo "快速检查命令:"
echo "1. 查看内存: free -h"
echo "2. 查看磁盘: df -h"
echo "3. 查看进程: ps aux | grep python"
echo "4. 查看端口: netstat -tlnp | grep 800"
echo "5. 查看MySQL: systemctl status mysql"
