# SSL攻击防护配置
# 添加到nginx配置中

# 限制SSL握手频率
limit_req_zone $binary_remote_addr zone=ssl_limit:10m rate=10r/m;

# 限制连接数
limit_conn_zone $binary_remote_addr zone=ssl_conn:10m;

server {
    # SSL连接限制
    limit_conn ssl_conn 5;
    limit_req zone=ssl_limit burst=5 nodelay;
    
    # SSL配置优化
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 防止SSL重协商攻击
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 阻止恶意SSL请求
    error_page 400 /400.html;
    location = /400.html {
        root /www/wwwroot/game;
        internal;
    }
    
    # 记录SSL攻击
    access_log /var/log/nginx/ssl_attacks.log combined if=$ssl_attack;
    
    # 检测SSL攻击
    map $request $ssl_attack {
        ~^\x16\x03 1;
        default 0;
    }
    
    # 如果检测到SSL攻击，返回444
    if ($ssl_attack) {
        return 444;
    }
}
