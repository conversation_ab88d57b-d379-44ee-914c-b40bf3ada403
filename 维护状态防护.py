#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
维护状态防护脚本
防止维护状态被意外修改，确保游戏正常运行
"""

import os
import sys
import django
import time
import datetime

# 添加项目路径
sys.path.append('server/trunk/llol')
sys.path.append('server/trunk/game_lib')

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'llol.settings')
django.setup()

from game_lib.models.main import CacheTable

class MaintainProtector:
    def __init__(self):
        self.target_full_status = 1  # 目标：正常运行
        self.target_zone_status = 1  # 目标：正常运行
        self.check_interval = 30     # 检查间隔30秒
        self.log_file = f"maintain_protector_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
        
    def log(self, message):
        """记录日志"""
        timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_msg = f"[{timestamp}] {message}"
        print(log_msg)
        
        # 写入文件
        with open(self.log_file, 'a', encoding='utf-8') as f:
            f.write(log_msg + '\n')
    
    def check_and_fix(self):
        """检查并修复维护状态"""
        try:
            # 获取当前状态
            full_maintain_status = CacheTable.get('full_maintain_status', 1)
            zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
            
            fixed = False
            
            # 检查全服维护状态
            if full_maintain_status != self.target_full_status:
                self.log(f"🚨 检测到全服维护状态异常: {full_maintain_status} → {self.target_full_status}")
                CacheTable.set('full_maintain_status', self.target_full_status, 3600*24*365*100)
                self.log("✅ 已自动修复全服维护状态")
                fixed = True
            
            # 检查分区维护状态
            if zone_maintain_status != self.target_zone_status:
                self.log(f"🚨 检测到分区维护状态异常: {zone_maintain_status} → {self.target_zone_status}")
                CacheTable.set('zone_maintain_status', self.target_zone_status, 3600*24*365*100)
                self.log("✅ 已自动修复分区维护状态")
                fixed = True
            
            if not fixed:
                # 每10分钟输出一次正常状态
                if int(time.time()) % 600 == 0:
                    self.log("✅ 维护状态正常")
            
            return True
            
        except Exception as e:
            self.log(f"❌ 检查修复过程中出现错误: {e}")
            return False
    
    def start_protection(self):
        """开始保护"""
        self.log("🛡️  启动维护状态防护...")
        self.log(f"   目标全服状态: {self.target_full_status} (1=正常)")
        self.log(f"   目标分区状态: {self.target_zone_status} (1=正常)")
        self.log(f"   检查间隔: {self.check_interval}秒")
        self.log(f"   日志文件: {self.log_file}")
        
        try:
            while True:
                self.check_and_fix()
                time.sleep(self.check_interval)
                
        except KeyboardInterrupt:
            self.log("⏹️  防护被用户中断")
        except Exception as e:
            self.log(f"❌ 防护过程中出现错误: {e}")
            # 重新启动防护
            time.sleep(60)
            self.start_protection()

def create_service_script():
    """创建系统服务脚本"""
    service_script = """#!/bin/bash
# 维护状态防护服务脚本

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PYTHON_SCRIPT="$SCRIPT_DIR/维护状态防护.py"
PID_FILE="$SCRIPT_DIR/maintain_protector.pid"
LOG_FILE="$SCRIPT_DIR/maintain_protector_service.log"

start() {
    if [ -f "$PID_FILE" ]; then
        echo "防护服务已在运行 (PID: $(cat $PID_FILE))"
        return 1
    fi
    
    echo "启动维护状态防护服务..."
    nohup python "$PYTHON_SCRIPT" >> "$LOG_FILE" 2>&1 &
    echo $! > "$PID_FILE"
    echo "防护服务已启动 (PID: $!)"
}

stop() {
    if [ ! -f "$PID_FILE" ]; then
        echo "防护服务未运行"
        return 1
    fi
    
    PID=$(cat "$PID_FILE")
    echo "停止维护状态防护服务 (PID: $PID)..."
    kill "$PID"
    rm -f "$PID_FILE"
    echo "防护服务已停止"
}

status() {
    if [ -f "$PID_FILE" ]; then
        PID=$(cat "$PID_FILE")
        if ps -p "$PID" > /dev/null; then
            echo "防护服务正在运行 (PID: $PID)"
        else
            echo "防护服务已停止 (PID文件存在但进程不存在)"
            rm -f "$PID_FILE"
        fi
    else
        echo "防护服务未运行"
    fi
}

case "$1" in
    start)
        start
        ;;
    stop)
        stop
        ;;
    restart)
        stop
        sleep 2
        start
        ;;
    status)
        status
        ;;
    *)
        echo "用法: $0 {start|stop|restart|status}"
        exit 1
        ;;
esac
"""
    
    with open('maintain_protector_service.sh', 'w') as f:
        f.write(service_script)
    
    # 设置执行权限
    os.chmod('maintain_protector_service.sh', 0o755)
    
    print("✅ 已创建服务脚本: maintain_protector_service.sh")
    print("使用方法:")
    print("  启动: ./maintain_protector_service.sh start")
    print("  停止: ./maintain_protector_service.sh stop")
    print("  重启: ./maintain_protector_service.sh restart")
    print("  状态: ./maintain_protector_service.sh status")

def main():
    print("=" * 60)
    print("游戏维护状态防护工具")
    print("=" * 60)
    print("此工具将持续监控并自动修复维护状态，确保游戏正常运行")
    print()
    
    choice = input("选择运行模式:\n1. 直接运行防护\n2. 创建系统服务脚本\n请输入选择 (1/2): ")
    
    if choice == "2":
        create_service_script()
    else:
        protector = MaintainProtector()
        protector.start_protection()

if __name__ == "__main__":
    main()
