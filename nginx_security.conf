# Nginx安全配置文件
# 将此配置添加到您的nginx配置中

# 限制请求频率
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=api:10m rate=30r/m;
limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;

server {
    # 限制连接数
    limit_conn conn_limit_per_ip 10;
    limit_req zone=api burst=20 nodelay;
    
    # 隐藏Nginx版本
    server_tokens off;
    
    # 阻止常见扫描路径
    location ~ ^/(nmaplowercheck|evox|sdk|HNAP1|admin|phpmyadmin|wp-admin) {
        deny all;
        return 444;
    }
    
    # 阻止可疑User-Agent
    if ($http_user_agent ~* (nmap|nikto|sqlmap|masscan|zmap)) {
        return 444;
    }
    
    # 阻止空User-Agent
    if ($http_user_agent = "") {
        return 444;
    }
    
    # 只允许特定请求方法
    if ($request_method !~ ^(GET|POST|HEAD)$) {
        return 444;
    }
    
    # 阻止过长的URI
    if ($request_uri ~ "^.{255,}") {
        return 444;
    }
    
    # 创建404页面
    error_page 404 /404.html;
    location = /404.html {
        root /www/wwwroot/game;
        internal;
    }
    
    # 记录可疑访问
    access_log /var/log/nginx/security.log combined;
}

# 地理位置限制（可选）
# 如果您的游戏只面向特定地区，可以启用
# geo $allowed_country {
#     default no;
#     CN yes;  # 只允许中国IP
# }
# 
# map $allowed_country $blocked_country {
#     no 1;
#     yes 0;
# }
# 
# if ($blocked_country) {
#     return 444;
# }
