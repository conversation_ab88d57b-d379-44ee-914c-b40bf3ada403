#!/bin/bash
# MySQL数据库备份脚本 (Linux/Mac版本)

echo "=========================================="
echo "           MySQL数据库备份工具"
echo "=========================================="
echo

# 设置变量（请根据实际情况修改）
MYSQL_USER="root"
MYSQL_HOST="localhost"
MYSQL_PORT="3306"

# 创建备份目录
BACKUP_DIR="database_backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$BACKUP_DIR"

# 获取MySQL密码
echo -n "请输入MySQL密码: "
read -s MYSQL_PASSWORD
echo

echo
echo "选择备份方式:"
echo "1. 备份单个数据库"
echo "2. 备份所有数据库"
echo "3. 备份指定表"
echo "4. 查看所有数据库列表"
echo -n "请输入选择 (1-4): "
read CHOICE

case $CHOICE in
    1)
        echo
        echo -n "请输入要备份的数据库名: "
        read DB_NAME
        echo "正在备份数据库: $DB_NAME"
        
        BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_backup_$(date +%Y%m%d_%H%M%S).sql"
        
        mysqldump -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h"$MYSQL_HOST" -P"$MYSQL_PORT" \
                  --routines --triggers --single-transaction "$DB_NAME" > "$BACKUP_FILE"
        
        if [ $? -eq 0 ]; then
            echo "✅ 备份成功！"
            echo "备份文件: $BACKUP_FILE"
        else
            echo "❌ 备份失败！"
        fi
        ;;
        
    2)
        echo
        echo "正在备份所有数据库..."
        
        BACKUP_FILE="$BACKUP_DIR/all_databases_backup_$(date +%Y%m%d_%H%M%S).sql"
        
        mysqldump -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h"$MYSQL_HOST" -P"$MYSQL_PORT" \
                  --routines --triggers --single-transaction --all-databases > "$BACKUP_FILE"
        
        if [ $? -eq 0 ]; then
            echo "✅ 备份成功！"
            echo "备份文件: $BACKUP_FILE"
        else
            echo "❌ 备份失败！"
        fi
        ;;
        
    3)
        echo
        echo -n "请输入数据库名: "
        read DB_NAME
        echo -n "请输入表名（多个表用空格分隔）: "
        read TABLE_NAMES
        echo "正在备份指定表..."
        
        BACKUP_FILE="$BACKUP_DIR/${DB_NAME}_tables_backup_$(date +%Y%m%d_%H%M%S).sql"
        
        mysqldump -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h"$MYSQL_HOST" -P"$MYSQL_PORT" \
                  --routines --triggers --single-transaction "$DB_NAME" $TABLE_NAMES > "$BACKUP_FILE"
        
        if [ $? -eq 0 ]; then
            echo "✅ 备份成功！"
            echo "备份文件: $BACKUP_FILE"
        else
            echo "❌ 备份失败！"
        fi
        ;;
        
    4)
        echo
        echo "正在获取数据库列表..."
        mysql -u"$MYSQL_USER" -p"$MYSQL_PASSWORD" -h"$MYSQL_HOST" -P"$MYSQL_PORT" -e "SHOW DATABASES;"
        echo
        echo "按任意键继续..."
        read
        exec "$0"  # 重新运行脚本
        ;;
        
    *)
        echo "无效选择！"
        exit 1
        ;;
esac

echo
echo "备份完成！文件保存在: $BACKUP_DIR"
echo

# 询问是否压缩
echo -n "是否压缩备份文件？(y/n): "
read COMPRESS

if [[ "$COMPRESS" =~ ^[Yy]$ ]]; then
    echo "正在压缩备份文件..."
    tar -czf "${BACKUP_DIR}_compressed.tar.gz" "$BACKUP_DIR"
    echo "✅ 压缩完成: ${BACKUP_DIR}_compressed.tar.gz"
fi

echo
echo "备份任务完成！"

# 显示文件大小
echo "备份文件信息:"
ls -lh "$BACKUP_DIR"/*
if [ -f "${BACKUP_DIR}_compressed.tar.gz" ]; then
    ls -lh "${BACKUP_DIR}_compressed.tar.gz"
fi
