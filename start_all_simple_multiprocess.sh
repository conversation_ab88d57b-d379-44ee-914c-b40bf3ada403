#!/bin/bash

# 简单多进程版游戏服务启动脚本（基于原版改进）

LOG_DIR="./server/logs"
mkdir -p "$LOG_DIR"

# 函数：检查进程是否已运行
is_running() {
    pgrep -f "$1" >/dev/null 2>&1
}

# 函数：启动服务
start_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在检查 [$cmd]" | tee -a "$log_file"

    if is_running "$cmd"; then
        echo "[$(date)] $name 已在运行，跳过启动。" | tee -a "$log_file"
    else
        echo "[$(date)] 启动 $name ..." | tee -a "$log_file"
        nohup $cmd >> "$log_file" 2>&1 &
        echo "[$(date)] $name 启动完成！PID: $!" | tee -a "$log_file"
    fi
}

# 函数：停止服务
stop_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在停止 $name ..." | tee -a "$log_file"

    # 查找并杀死匹配的进程
    PIDS=$(pgrep -f "$cmd")
    if [ -n "$PIDS" ]; then
        echo "[$(date)] 找到以下 PID: $PIDS，正在终止..." | tee -a "$log_file"
        echo "$PIDS" | xargs kill -9 2>/dev/null
        echo "[$(date)] $name 已停止。" | tee -a "$log_file"
    else
        echo "[$(date)] 没有找到正在运行的 $name 进程。" | tee -a "$log_file"
    fi
}

ACTION="$1"

if [ "$ACTION" == "start" ]; then

    echo "========== $(date) 开始启动所有服务（简单多进程版） =========="

    # 1. 启动统计服务（多个端口实现多进程效果）
    start_service "Python_Analytics_7701" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701"
    start_service "Python_Analytics_7702" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7702"
    start_service "Python_Analytics_7703" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7703"

    # 2. 启动后台服务（多个端口实现多进程效果）
    start_service "Python_LLOL_8500" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8500"
    start_service "Python_LLOL_8501" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8501"
    start_service "Python_LLOL_8502" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8502"
    start_service "Python_LLOL_8503" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8503"

    # 3. 启动战斗服（Node.js cluster多进程）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        
        # 创建简单的cluster版本
        if [ ! -f "LayaSimpleCluster.js" ]; then
            cat > LayaSimpleCluster.js << 'EOF'
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;

if (cluster.isMaster) {
    console.log(`主进程 ${process.pid} 启动`);
    
    // 启动2-4个工作进程
    const workerCount = Math.min(4, Math.max(2, Math.floor(numCPUs / 2)));
    console.log(`启动 ${workerCount} 个工作进程`);
    
    for (let i = 0; i < workerCount; i++) {
        cluster.fork();
    }
    
    cluster.on('exit', (worker, code, signal) => {
        console.log(`工作进程 ${worker.process.pid} 退出`);
        cluster.fork();
    });
    
} else {
    // 工作进程运行原始Laya逻辑
    console.log(`工作进程 ${process.pid} 启动`);
    require('./Laya.js');
}
EOF
        fi
        
        start_service "Node_Laya_SimpleCluster_3001" "node LayaSimpleCluster.js 3001"
    )

    # 4. 启动游戏区服备份
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Backup_SG3" "python backup_start_sg3.py"
    )
    
    # 5. 启动1区服务（2个实例）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5001" "python server.py --zone=1"
        # 如果server.py支持端口参数，启动第二个实例
        if python server.py --help 2>/dev/null | grep -q "port"; then
            start_service "Python_Zone_5001_2" "python server.py --zone=1 --port=5011"
        fi
    )
    
    # 6. 启动2区服务（2个实例）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5002" "python server.py --zone=2"
        if python server.py --help 2>/dev/null | grep -q "port"; then
            start_service "Python_Zone_5002_2" "python server.py --zone=2 --port=5012"
        fi
    )
    
    # 7. 启动3区服务（2个实例）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5003" "python server.py --zone=3"
        if python server.py --help 2>/dev/null | grep -q "port"; then
            start_service "Python_Zone_5003_2" "python server.py --zone=3 --port=5013"
        fi
    )

    # 8. 启动4区服务（2个实例）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5004" "python server.py --zone=4"
        if python server.py --help 2>/dev/null | grep -q "port"; then
            start_service "Python_Zone_5004_2" "python server.py --zone=4 --port=5014"
        fi
    )

    # 9. 启动5区服务（2个实例）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5005" "python server.py --zone=5"
        if python server.py --help 2>/dev/null | grep -q "port"; then
            start_service "Python_Zone_5005_2" "python server.py --zone=5 --port=5015"
        fi
    )

    echo ""
    echo "========== 简单多进程部署完成 =========="
    echo "统计服务: 3个实例 (端口 7701-7703)"
    echo "后台服务: 4个实例 (端口 8500-8503)"
    echo "战斗服务: Node.js cluster (端口 3001)"
    echo "区服服务: 每区1-2个实例"
    echo "========== $(date) 所有服务启动完成（简单多进程版） =========="

elif [ "$ACTION" == "stop" ]; then

    echo "========== $(date) 开始停止所有服务 =========="

    # 停止统计服务
    stop_service "Python_Analytics_7701" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701"
    stop_service "Python_Analytics_7702" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7702"
    stop_service "Python_Analytics_7703" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7703"

    # 停止后台服务
    stop_service "Python_LLOL_8500" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8500"
    stop_service "Python_LLOL_8501" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8501"
    stop_service "Python_LLOL_8502" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8502"
    stop_service "Python_LLOL_8503" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8503"

    # 停止战斗服
    stop_service "Node_Laya_SimpleCluster_3001" "node LayaSimpleCluster.js 3001"

    # 停止区服服务
    stop_service "Python_Backup_SG3" "python backup_start_sg3.py"
    stop_service "Python_Zone_5001" "python server.py --zone=1"
    stop_service "Python_Zone_5002" "python server.py --zone=2"
    stop_service "Python_Zone_5003" "python server.py --zone=3"
    stop_service "Python_Zone_5001_2" "python server.py --zone=1 --port=5011"
    stop_service "Python_Zone_5002_2" "python server.py --zone=2 --port=5012"
    stop_service "Python_Zone_5003_2" "python server.py --zone=3 --port=5013"
    
    echo "========== $(date) 所有服务已停止 =========="

else
    echo "用法: $0 [start|stop]"
    echo "简单多进程版启动脚本"
    echo ""
    echo "特点："
    echo "- 使用原版Django runserver启动多个实例"
    echo "- 使用Node.js简单cluster模式"
    echo "- 不依赖复杂的multiprocessing模块"
    echo "- 基于原版脚本改进，稳定可靠"
    echo ""
    echo "多进程架构："
    echo "- 统计服务: 3个Django实例 (7701-7703端口)"
    echo "- 后台服务: 4个Django实例 (8500-8503端口)"  
    echo "- 战斗服务: Node.js cluster (3001端口)"
    echo "- 区服务: 每区1-2个实例"
    echo ""
    echo "其他版本："
    echo "  原版:     ./start_all.sh start"
    echo "  复杂版:   ./start_all_multiprocess.sh start"
    exit 1
fi
