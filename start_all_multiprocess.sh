#!/bin/bash

# 纯多进程版游戏服务启动脚本（不依赖gunicorn）

LOG_DIR="./server/logs"
mkdir -p "$LOG_DIR"

# 函数：检查进程是否已运行
is_running() {
    pgrep -f "$1" >/dev/null 2>&1
}

# 函数：启动服务
start_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在检查 [$cmd]" | tee -a "$log_file"

    if is_running "$cmd"; then
        echo "[$(date)] $name 已在运行，跳过启动。" | tee -a "$log_file"
    else
        echo "[$(date)] 启动 $name ..." | tee -a "$log_file"
        nohup $cmd >> "$log_file" 2>&1 &
        echo "[$(date)] $name 启动完成！PID: $!" | tee -a "$log_file"
    fi
}

# 函数：停止服务
stop_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在停止 $name ..." | tee -a "$log_file"

    # 查找并杀死匹配的进程
    PIDS=$(pgrep -f "$cmd")
    if [ -n "$PIDS" ]; then
        echo "[$(date)] 找到以下 PID: $PIDS，正在终止..." | tee -a "$log_file"
        echo "$PIDS" | xargs kill -9 2>/dev/null
        echo "[$(date)] $name 已停止。" | tee -a "$log_file"
    else
        echo "[$(date)] 没有找到正在运行的 $name 进程。" | tee -a "$log_file"
    fi
}

# 函数：创建多进程Django服务器
create_multiprocess_django() {
    local service_name="$1"
    local base_port="$2"
    local manage_path="$3"
    local process_count="$4"
    
    cat > "./server/multiprocess_${service_name}.py" << EOF
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import multiprocessing
import time
from multiprocessing import Process

# 添加Django路径
sys.path.insert(0, '${manage_path}')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

import django
django.setup()

def run_django_server(port):
    """运行Django开发服务器"""
    from django.core.management import execute_from_command_line
    
    print(f"启动${service_name}进程，端口: {port}, PID: {os.getpid()}")
    
    # 使用Django的runserver命令
    sys.argv = ['manage.py', 'runserver', f'0.0.0.0:{port}']
    execute_from_command_line(sys.argv)

def main():
    processes = []
    base_port = ${base_port}
    process_count = ${process_count}
    
    print(f"启动${service_name}多进程服务器，基础端口: {base_port}, 进程数: {process_count}")
    
    try:
        # 启动多个Django进程
        for i in range(process_count):
            port = base_port + i
            p = Process(target=run_django_server, args=(port,))
            p.start()
            processes.append(p)
            print(f"${service_name}进程 {i+1} 启动在端口 {port}, PID: {p.pid}")
            time.sleep(1)  # 避免端口冲突
        
        # 等待所有进程
        for p in processes:
            p.join()
            
    except KeyboardInterrupt:
        print(f"停止${service_name}多进程服务器...")
        for p in processes:
            p.terminate()
            p.join()

if __name__ == '__main__':
    main()
EOF
    
    chmod +x "./server/multiprocess_${service_name}.py"
    echo "✅ 创建多进程${service_name}服务器: ./server/multiprocess_${service_name}.py"
}

# 函数：创建多进程Node.js服务器
create_multiprocess_nodejs() {
    cat > "./server/service/LayaMultiProcess.js" << 'EOF'
const cluster = require('cluster');
const numCPUs = require('os').cpus().length;
const http = require('http');
const url = require('url');
const request = require('request');

// 加载LayaSample
require('./LayaSample.max');

const port = parseInt(process.argv[2]) || 3000;

if (cluster.isMaster) {
    console.log(`Node.js多进程主进程 ${process.pid} 启动`);
    
    // 计算工作进程数（CPU核心数的一半，最少2个，最多8个）
    const workerCount = Math.min(8, Math.max(2, Math.floor(numCPUs / 2)));
    console.log(`CPU核心数: ${numCPUs}, 启动工作进程数: ${workerCount}`);
    
    // 获取游戏配置
    const api_url = 'http://**************:8500/gateway/';
    
    request.post(
        api_url, 
        {json: {"id":"33","method":"sys.get_config","params":{'pf': 'local'},"cfg_version":"","app_version":"1.0.0"}},
        function(error, response, body) {
            if (body && body.data) {
                sg.cfg.ConfigServer.formatTo(body.data);
                console.log('游戏配置加载完成');
                
                // 启动工作进程
                for (let i = 0; i < workerCount; i++) {
                    const worker = cluster.fork();
                    console.log(`工作进程 ${i+1} 启动, PID: ${worker.process.pid}`);
                }
            } else {
                console.error('获取游戏配置失败，使用默认配置启动');
                // 即使配置失败也启动工作进程
                for (let i = 0; i < workerCount; i++) {
                    const worker = cluster.fork();
                    console.log(`工作进程 ${i+1} 启动, PID: ${worker.process.pid}`);
                }
            }
        }
    );
    
    // 监听工作进程退出
    cluster.on('exit', (worker, code, signal) => {
        console.log(`工作进程 ${worker.process.pid} 退出，代码: ${code}, 信号: ${signal}`);
        console.log('重新启动工作进程...');
        cluster.fork();
    });
    
    // 监听工作进程在线
    cluster.on('online', (worker) => {
        console.log(`工作进程 ${worker.process.pid} 在线`);
    });
    
} else {
    // 工作进程逻辑
    console.log(`Node.js工作进程 ${process.pid} 启动，监听端口 ${port}`);
    
    const server = http.createServer(function(req, res) {
        let post = '';
        
        req.on('data', function(chunk) {
            post += chunk;
        });
        
        const pathName = url.parse(req.url).pathname;
        
        req.on('end', function() {
            try {
                // 设置响应头
                res.writeHead(200, {
                    'Content-Type': 'application/json',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Headers': 'Content-Type'
                });
                
                // 处理战斗请求
                if (pathName === '/fight' && post) {
                    const fightData = JSON.parse(post);
                    const result = sg.fight.logic.utils.FightInterface.doFight(fightData);
                    res.end(JSON.stringify(result));
                } else if (pathName === '/status') {
                    // 状态检查
                    res.end(JSON.stringify({
                        status: 'ok',
                        worker_pid: process.pid,
                        memory: process.memoryUsage(),
                        uptime: process.uptime()
                    }));
                } else {
                    // 其他请求
                    res.end(JSON.stringify({
                        status: 'ok',
                        worker_pid: process.pid,
                        path: pathName
                    }));
                }
                
            } catch (error) {
                console.error(`工作进程 ${process.pid} 处理错误:`, error);
                res.writeHead(500, {'Content-Type': 'application/json'});
                res.end(JSON.stringify({
                    error: error.message,
                    worker_pid: process.pid
                }));
            }
        });
    });
    
    server.listen(port, () => {
        console.log(`工作进程 ${process.pid} 监听端口 ${port}`);
    });
    
    // 优雅关闭
    process.on('SIGTERM', () => {
        console.log(`工作进程 ${process.pid} 收到SIGTERM信号，准备关闭`);
        server.close(() => {
            console.log(`工作进程 ${process.pid} 已关闭`);
            process.exit(0);
        });
    });
}
EOF
    
    echo "✅ 创建多进程Node.js服务器: ./server/service/LayaMultiProcess.js"
}

# 函数：创建负载均衡配置
create_nginx_loadbalancer() {
    cat > "./nginx_multiprocess.conf" << 'EOF'
# Nginx负载均衡配置（可选）

upstream analytics_backend {
    server 127.0.0.1:7701;
    server 127.0.0.1:7702;
    server 127.0.0.1:7703;
}

upstream llol_backend {
    server 127.0.0.1:8500;
    server 127.0.0.1:8501;
    server 127.0.0.1:8502;
    server 127.0.0.1:8503;
}

server {
    listen 7700;
    location / {
        proxy_pass http://analytics_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}

server {
    listen 8499;
    location / {
        proxy_pass http://llol_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF
    
    echo "✅ 创建Nginx负载均衡配置: ./nginx_multiprocess.conf"
}

ACTION="$1"

if [ "$ACTION" == "start" ]; then

    echo "========== $(date) 开始启动所有服务（纯多进程版） =========="

    # 创建多进程服务器文件
    echo "创建多进程服务器配置文件..."
    create_multiprocess_django "analytics" "7701" "./server/analytics_sg3/trunk/src" "3"
    create_multiprocess_django "backend" "8500" "./server/trunk/llol/src" "4"
    create_multiprocess_nodejs
    create_nginx_loadbalancer

    # 1. 启动统计服务（3个进程：7701, 7702, 7703）
    start_service "Python_Analytics_MultiProcess" "python ./server/multiprocess_analytics.py"

    # 2. 启动后台服务（4个进程：8500, 8501, 8502, 8503）
    start_service "Python_LLOL_MultiProcess" "python ./server/multiprocess_backend.py"

    # 3. 启动战斗服（Node.js cluster多进程）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Node_Laya_MultiProcess_3001" "node LayaMultiProcess.js 3001"
    )

    # 4. 启动游戏区服备份
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Backup_SG3" "python backup_start_sg3.py"
    )
    
    # 5. 启动1区服务（2个进程）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5001_1" "python server.py --zone=1 --port=5001"
        start_service "Python_Zone_5001_2" "python server.py --zone=1 --port=5011"
    )
    
    # 6. 启动2区服务（2个进程）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5002_1" "python server.py --zone=2 --port=5002"
        start_service "Python_Zone_5002_2" "python server.py --zone=2 --port=5012"
    )
    
    # 7. 启动3区服务（2个进程）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5003_1" "python server.py --zone=3 --port=5003"
        start_service "Python_Zone_5003_2" "python server.py --zone=3 --port=5013"
    )

    echo ""
    echo "========== 多进程部署完成 =========="
    echo "统计服务: 3个进程 (端口 7701-7703)"
    echo "后台服务: 4个进程 (端口 8500-8503)"
    echo "战斗服务: cluster多进程 (端口 3001)"
    echo "1区服务: 2个进程 (端口 5001, 5011)"
    echo "2区服务: 2个进程 (端口 5002, 5012)"
    echo "3区服务: 2个进程 (端口 5003, 5013)"
    echo "========== $(date) 所有服务启动完成（纯多进程版） =========="

elif [ "$ACTION" == "stop" ]; then

    echo "========== $(date) 开始停止所有服务 =========="

    # 停止多进程服务
    stop_service "Python_Analytics_MultiProcess" "python ./server/multiprocess_analytics.py"
    stop_service "Python_LLOL_MultiProcess" "python ./server/multiprocess_backend.py"
    stop_service "Node_Laya_MultiProcess_3001" "node LayaMultiProcess.js 3001"
    
    # 停止区服服务
    stop_service "Python_Backup_SG3" "python backup_start_sg3.py"
    stop_service "Python_Zone_5001_1" "python server.py --zone=1 --port=5001"
    stop_service "Python_Zone_5001_2" "python server.py --zone=1 --port=5011"
    stop_service "Python_Zone_5002_1" "python server.py --zone=2 --port=5002"
    stop_service "Python_Zone_5002_2" "python server.py --zone=2 --port=5012"
    stop_service "Python_Zone_5003_1" "python server.py --zone=3 --port=5003"
    stop_service "Python_Zone_5003_2" "python server.py --zone=3 --port=5013"
    
    echo "========== $(date) 所有服务已停止 =========="

else
    echo "用法: $0 [start|stop]"
    echo "纯多进程版启动脚本 - 不依赖gunicorn"
    echo ""
    echo "多进程架构："
    echo "- 统计服务: 3个Django进程 (7701-7703端口)"
    echo "- 后台服务: 4个Django进程 (8500-8503端口)"  
    echo "- 战斗服务: Node.js cluster多进程 (3001端口)"
    echo "- 区服务: 每区2个进程 (原端口+10的端口)"
    echo ""
    echo "特点："
    echo "- 不依赖gunicorn，使用Python原生multiprocessing"
    echo "- 不依赖外部工具，使用Node.js原生cluster"
    echo "- 真正的多进程并行处理"
    echo "- 进程间完全隔离，故障不互相影响"
    echo ""
    echo "其他版本："
    echo "  原版:     ./start_all.sh start"
    echo "  优化版:   ./start_all_optimized.sh start"
    exit 1
fi
