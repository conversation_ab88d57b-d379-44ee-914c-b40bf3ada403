#!/bin/bash

# 多进程测试脚本 - 诊断问题

echo "=========================================="
echo "        多进程启动测试诊断"
echo "=========================================="

# 检查目录结构
echo "1. 检查目录结构："
echo "当前目录: $(pwd)"
echo "server目录: $(ls -la server/ 2>/dev/null | head -5)"
echo "analytics目录: $(ls -la server/analytics_sg3/trunk/src/ 2>/dev/null | head -3)"
echo "backend目录: $(ls -la server/trunk/llol/src/ 2>/dev/null | head -3)"
echo ""

# 创建日志目录
echo "2. 创建日志目录："
mkdir -p ./server/logs
chmod 755 ./server/logs
echo "✅ 日志目录创建完成: ./server/logs"
echo ""

# 测试Django路径
echo "3. 测试Django路径："
if [ -d "./server/analytics_sg3/trunk/src" ]; then
    echo "✅ 统计服务路径存在"
    if [ -f "./server/analytics_sg3/trunk/src/manage.py" ]; then
        echo "✅ 统计服务manage.py存在"
    else
        echo "❌ 统计服务manage.py不存在"
    fi
else
    echo "❌ 统计服务路径不存在"
fi

if [ -d "./server/trunk/llol/src" ]; then
    echo "✅ 后台服务路径存在"
    if [ -f "./server/trunk/llol/src/manage.py" ]; then
        echo "✅ 后台服务manage.py存在"
    else
        echo "❌ 后台服务manage.py不存在"
    fi
else
    echo "❌ 后台服务路径不存在"
fi
echo ""

# 创建简化的多进程测试脚本
echo "4. 创建简化测试脚本："

# 统计服务测试脚本
cat > ./server/test_analytics.py << 'EOF'
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import time

print(f"测试统计服务启动，PID: {os.getpid()}")
print(f"当前目录: {os.getcwd()}")

# 检查Django路径
analytics_path = "./server/analytics_sg3/trunk/src"
if os.path.exists(analytics_path):
    print(f"✅ 找到统计服务路径: {analytics_path}")
    sys.path.insert(0, analytics_path)
    os.chdir(analytics_path)
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
        import django
        django.setup()
        print("✅ Django初始化成功")
        
        # 简单的HTTP服务器
        from django.core.wsgi import get_wsgi_application
        from wsgiref.simple_server import make_server
        
        application = get_wsgi_application()
        httpd = make_server('0.0.0.0', 7701, application)
        print("✅ 统计服务启动在端口 7701")
        
        # 运行服务器
        httpd.serve_forever()
        
    except Exception as e:
        print(f"❌ Django启动失败: {e}")
        import traceback
        traceback.print_exc()
else:
    print(f"❌ 统计服务路径不存在: {analytics_path}")

print("统计服务测试结束")
EOF

# 后台服务测试脚本
cat > ./server/test_backend.py << 'EOF'
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import time

print(f"测试后台服务启动，PID: {os.getpid()}")
print(f"当前目录: {os.getcwd()}")

# 检查Django路径
backend_path = "./server/trunk/llol/src"
if os.path.exists(backend_path):
    print(f"✅ 找到后台服务路径: {backend_path}")
    sys.path.insert(0, backend_path)
    os.chdir(backend_path)
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
        import django
        django.setup()
        print("✅ Django初始化成功")
        
        # 简单的HTTP服务器
        from django.core.wsgi import get_wsgi_application
        from wsgiref.simple_server import make_server
        
        application = get_wsgi_application()
        httpd = make_server('0.0.0.0', 8500, application)
        print("✅ 后台服务启动在端口 8500")
        
        # 运行服务器
        httpd.serve_forever()
        
    except Exception as e:
        print(f"❌ Django启动失败: {e}")
        import traceback
        traceback.print_exc()
else:
    print(f"❌ 后台服务路径不存在: {backend_path}")

print("后台服务测试结束")
EOF

# Node.js测试脚本
cat > ./server/service/test_nodejs.js << 'EOF'
const http = require('http');
const port = 3001;

console.log(`测试Node.js服务启动，PID: ${process.pid}`);
console.log(`当前目录: ${process.cwd()}`);

// 检查LayaSample文件
const fs = require('fs');
if (fs.existsSync('./LayaSample.max.js')) {
    console.log('✅ 找到LayaSample.max.js');
    try {
        require('./LayaSample.max.js');
        console.log('✅ LayaSample加载成功');
    } catch (e) {
        console.log('❌ LayaSample加载失败:', e.message);
    }
} else {
    console.log('❌ LayaSample.max.js不存在');
}

// 创建简单HTTP服务器
const server = http.createServer((req, res) => {
    res.writeHead(200, {'Content-Type': 'application/json'});
    res.end(JSON.stringify({
        status: 'ok',
        pid: process.pid,
        path: req.url
    }));
});

server.listen(port, () => {
    console.log(`✅ Node.js测试服务启动在端口 ${port}`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到SIGTERM信号，关闭服务器');
    server.close(() => {
        process.exit(0);
    });
});
EOF

chmod +x ./server/test_*.py
chmod +x ./server/service/test_nodejs.js

echo "✅ 测试脚本创建完成"
echo ""

# 测试启动
echo "5. 测试单个服务启动："

echo "测试统计服务..."
nohup python ./server/test_analytics.py > ./server/logs/test_analytics.log 2>&1 &
ANALYTICS_PID=$!
echo "统计服务PID: $ANALYTICS_PID"

sleep 2

echo "测试后台服务..."
nohup python ./server/test_backend.py > ./server/logs/test_backend.log 2>&1 &
BACKEND_PID=$!
echo "后台服务PID: $BACKEND_PID"

sleep 2

echo "测试Node.js服务..."
cd ./server/service
nohup node test_nodejs.js > ../logs/test_nodejs.log 2>&1 &
NODEJS_PID=$!
echo "Node.js服务PID: $NODEJS_PID"
cd ../..

sleep 3

echo ""
echo "6. 检查测试结果："

# 检查进程是否还在运行
echo "进程状态："
if ps -p $ANALYTICS_PID > /dev/null 2>&1; then
    echo "✅ 统计服务进程运行中 (PID: $ANALYTICS_PID)"
else
    echo "❌ 统计服务进程已退出 (PID: $ANALYTICS_PID)"
fi

if ps -p $BACKEND_PID > /dev/null 2>&1; then
    echo "✅ 后台服务进程运行中 (PID: $BACKEND_PID)"
else
    echo "❌ 后台服务进程已退出 (PID: $BACKEND_PID)"
fi

if ps -p $NODEJS_PID > /dev/null 2>&1; then
    echo "✅ Node.js服务进程运行中 (PID: $NODEJS_PID)"
else
    echo "❌ Node.js服务进程已退出 (PID: $NODEJS_PID)"
fi

echo ""
echo "端口状态："
for port in 7701 8500 3001; do
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo "✅ 端口 $port: 监听中"
    else
        echo "❌ 端口 $port: 未监听"
    fi
done

echo ""
echo "7. 查看日志："
echo "统计服务日志:"
tail -10 ./server/logs/test_analytics.log 2>/dev/null || echo "日志文件不存在"

echo ""
echo "后台服务日志:"
tail -10 ./server/logs/test_backend.log 2>/dev/null || echo "日志文件不存在"

echo ""
echo "Node.js服务日志:"
tail -10 ./server/logs/test_nodejs.log 2>/dev/null || echo "日志文件不存在"

echo ""
echo "8. 清理测试进程："
echo "停止测试进程..."
kill $ANALYTICS_PID $BACKEND_PID $NODEJS_PID 2>/dev/null
sleep 2

echo ""
echo "=========================================="
echo "测试完成！请查看上面的输出来诊断问题。"
echo "如果服务启动失败，请检查日志文件中的错误信息。"
echo "=========================================="
