#!/bin/bash
# 实时攻击检测和自动封禁脚本

ACCESS_LOG="/var/log/nginx/access.log"
ALERT_LOG="attack_detection_$(date +%Y%m%d).log"
BLOCKED_IPS="/tmp/auto_blocked_ips.txt"

log_alert() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$ALERT_LOG"
}

# 检测SSL攻击
detect_ssl_attacks() {
    # 检测最近1分钟的SSL攻击
    local ssl_attacks=$(tail -100 "$ACCESS_LOG" | grep "$(date -d '1 minute ago' '+%d/%b/%Y:%H:%M')" | grep -E '\x16\x03|400.*"-" "-"' | awk '{print $1}' | sort | uniq -c | awk '$1 >= 3 {print $2}')
    
    for ip in $ssl_attacks; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            auto_block_ip "$ip" "SSL攻击"
        fi
    done
}

# 检测空请求攻击
detect_empty_requests() {
    # 检测空请求或无User-Agent的攻击
    local empty_attacks=$(tail -100 "$ACCESS_LOG" | grep "$(date -d '1 minute ago' '+%d/%b/%Y:%H:%M')" | grep -E '"".*400.*"-" "-"' | awk '{print $1}' | sort | uniq -c | awk '$1 >= 2 {print $2}')
    
    for ip in $empty_attacks; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            auto_block_ip "$ip" "空请求攻击"
        fi
    done
}

# 检测高频异常请求
detect_high_frequency_errors() {
    # 检测最近1分钟内400/404错误超过10次的IP
    local error_ips=$(tail -200 "$ACCESS_LOG" | grep "$(date -d '1 minute ago' '+%d/%b/%Y:%H:%M')" | grep -E ' (400|404) ' | awk '{print $1}' | sort | uniq -c | awk '$1 >= 10 {print $2}')
    
    for ip in $error_ips; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            auto_block_ip "$ip" "高频错误请求"
        fi
    done
}

# 检测扫描攻击
detect_scan_attacks() {
    # 检测扫描特征
    local scan_ips=$(tail -100 "$ACCESS_LOG" | grep "$(date -d '2 minutes ago' '+%d/%b/%Y:%H:%M')" | grep -E 'nmaplowercheck|evox|HNAP1|sdk|admin|wp-admin' | awk '{print $1}' | sort | uniq)
    
    for ip in $scan_ips; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            auto_block_ip "$ip" "端口扫描"
        fi
    done
}

# 自动封禁IP
auto_block_ip() {
    local ip=$1
    local reason=$2
    
    # 检查是否已经封禁
    if iptables -L INPUT -n | grep -q "$ip"; then
        return
    fi
    
    # 检查是否是内网IP
    if [[ $ip =~ ^(10\.|172\.(1[6-9]|2[0-9]|3[01])\.|192\.168\.|127\.) ]]; then
        return
    fi
    
    # 检查是否是已知的正常IP（可以添加白名单）
    # if [[ $ip =~ ^(你的正常IP段) ]]; then
    #     return
    # fi
    
    log_alert "🚨 自动封禁攻击IP: $ip (原因: $reason)"
    
    # 添加iptables规则
    iptables -A INPUT -s "$ip" -j DROP
    
    # 记录封禁信息
    echo "$(date '+%Y-%m-%d %H:%M:%S') $ip $reason" >> "$BLOCKED_IPS"
    
    # 保存iptables规则
    iptables-save > /etc/iptables/rules.v4
}

# 分析当前攻击趋势
analyze_attack_trends() {
    log_alert "========== 攻击趋势分析 =========="
    
    # 统计最近10分钟的攻击类型
    local recent_time=$(date -d '10 minutes ago' '+%d/%b/%Y:%H:%M')
    
    log_alert "SSL攻击次数: $(tail -1000 "$ACCESS_LOG" | grep "$recent_time" | grep -c '\x16\x03')"
    log_alert "空请求攻击: $(tail -1000 "$ACCESS_LOG" | grep "$recent_time" | grep -c '"".*400.*"-" "-"')"
    log_alert "扫描攻击次数: $(tail -1000 "$ACCESS_LOG" | grep "$recent_time" | grep -cE 'nmaplowercheck|evox|HNAP1')"
    log_alert "总错误请求: $(tail -1000 "$ACCESS_LOG" | grep "$recent_time" | grep -c ' 400 ')"
    
    # 统计攻击来源地区（需要GeoIP数据库）
    log_alert "当前封禁IP数量: $(iptables -L INPUT -n | grep DROP | wc -l)"
}

# 主监控循环
main_monitor() {
    log_alert "启动实时攻击检测系统..."
    
    local check_count=0
    
    while true; do
        check_count=$((check_count + 1))
        
        # 执行各种检测
        detect_ssl_attacks
        detect_empty_requests
        detect_high_frequency_errors
        detect_scan_attacks
        
        # 每10次检查输出一次分析报告
        if [ $((check_count % 10)) -eq 0 ]; then
            analyze_attack_trends
        fi
        
        # 每次检查后等待30秒
        sleep 30
    done
}

# 手动分析指定时间段
analyze_timeframe() {
    local start_time="$1"
    local end_time="$2"
    
    if [ -z "$start_time" ] || [ -z "$end_time" ]; then
        echo "用法: $0 analyze '27/Jul/2025:23:35' '27/Jul/2025:23:45'"
        exit 1
    fi
    
    log_alert "分析时间段: $start_time 到 $end_time"
    
    # 提取指定时间段的日志
    grep -E "$start_time|$end_time" "$ACCESS_LOG" | while read line; do
        # 检查是否包含攻击特征
        if echo "$line" | grep -qE '\x16\x03|"".*400.*"-" "-"|nmaplowercheck|evox'; then
            local ip=$(echo "$line" | awk '{print $1}')
            log_alert "发现攻击: $ip - $line"
        fi
    done
}

# 命令行参数处理
case "$1" in
    "monitor")
        main_monitor
        ;;
    "analyze")
        analyze_timeframe "$2" "$3"
        ;;
    "block-known")
        # 封禁已知攻击IP
        auto_block_ip "************" "空请求攻击"
        auto_block_ip "*************" "SSL攻击"
        auto_block_ip "**************" "扫描攻击"
        log_alert "已封禁已知攻击IP"
        ;;
    *)
        echo "用法: $0 {monitor|analyze <开始时间> <结束时间>|block-known}"
        echo "示例:"
        echo "  $0 monitor                                    # 启动实时监控"
        echo "  $0 analyze '27/Jul/2025:23:35' '27/Jul/2025:23:45'  # 分析指定时间段"
        echo "  $0 block-known                               # 封禁已知攻击IP"
        exit 1
        ;;
esac
