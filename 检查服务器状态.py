#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
检查服务器状态脚本
诊断游戏无法进入的问题
"""

import os
import sys
import django

# 添加项目路径
sys.path.append('server/trunk/llol')
sys.path.append('server/trunk/game_lib')

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'llol.settings')
django.setup()

from game_lib.models.main import CacheTable
from game_lib.models.zone import UserZone

def check_server_status():
    """检查服务器状态"""
    print("🔍 正在检查服务器状态...")
    
    try:
        # 1. 检查维护状态
        print("\n1. 维护状态检查:")
        full_maintain_status = CacheTable.get('full_maintain_status', 1)
        zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
        
        print(f"   全服维护状态: {full_maintain_status} {'✅ 正常' if full_maintain_status == 1 else '❌ 维护中'}")
        print(f"   分区维护状态: {zone_maintain_status} {'✅ 正常' if zone_maintain_status == 1 else '❌ 维护中'}")
        
        # 2. 检查维护配置
        print("\n2. 维护配置检查:")
        maintain_config = CacheTable.get('zone_maintain_config', '{}')
        maintain_config = eval(str(maintain_config))
        
        zone_list = maintain_config.get('zone_list', [])
        white_list = maintain_config.get('white_list', [])
        info_msg = maintain_config.get('info_msg', '')
        
        print(f"   维护区服列表: {zone_list}")
        print(f"   白名单用户: {white_list}")
        print(f"   维护提示信息: {info_msg}")
        
        # 3. 检查区服状态
        print("\n3. 区服状态检查:")
        try:
            zone_list_data = UserZone.get_zone_list('running')
            print(f"   运行中的区服数量: {len(zone_list_data)}")
            for zone in zone_list_data[:5]:  # 只显示前5个
                print(f"   区服 {zone[0]}: {zone[1]} - {zone[2]}")
        except Exception as e:
            print(f"   ❌ 获取区服列表失败: {e}")
        
        # 4. 检查缓存状态
        print("\n4. 缓存状态检查:")
        try:
            test_key = 'server_status_test'
            CacheTable.set(test_key, 'test_value', 60)
            test_value = CacheTable.get(test_key, None)
            if test_value == 'test_value':
                print("   ✅ 缓存服务正常")
                CacheTable.rem(test_key)
            else:
                print("   ❌ 缓存服务异常")
        except Exception as e:
            print(f"   ❌ 缓存测试失败: {e}")
        
        # 5. 问题诊断
        print("\n5. 问题诊断:")
        issues = []
        
        if full_maintain_status == 0:
            issues.append("全服处于维护状态")
        
        if zone_maintain_status == 0:
            issues.append("分区处于维护状态")
        
        if not issues:
            print("   ✅ 未发现明显问题")
            print("   建议检查:")
            print("   - 客户端版本是否匹配")
            print("   - 网络连接是否正常")
            print("   - 服务器进程是否运行")
        else:
            print("   ❌ 发现问题:")
            for issue in issues:
                print(f"   - {issue}")
        
        return issues
        
    except Exception as e:
        print(f"❌ 检查过程中出现错误: {e}")
        return ["检查过程异常"]

def get_fix_suggestions(issues):
    """获取修复建议"""
    if not issues:
        return
    
    print("\n🛠️ 修复建议:")
    
    if "全服处于维护状态" in issues:
        print("1. 修复全服维护状态:")
        print("   执行: python 修复维护状态.py")
        print("   或手动: CacheTable.set('full_maintain_status', 1, 3600*24*365*100)")
    
    if "分区处于维护状态" in issues:
        print("2. 修复分区维护状态:")
        print("   执行: python 修复维护状态.py")
        print("   或手动: CacheTable.set('zone_maintain_status', 1, 3600*24*365*100)")
    
    print("\n3. 通过管理后台修复:")
    print("   访问: http://你的服务器地址/admin/maintain/maintain/")
    print("   将维护状态改为'开服'")

if __name__ == "__main__":
    print("=" * 60)
    print("游戏服务器状态检查工具")
    print("=" * 60)
    
    issues = check_server_status()
    get_fix_suggestions(issues)
    
    print("\n" + "=" * 60)
    print("检查完成！")
    print("=" * 60)
