#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
修复游戏维护状态脚本
解决"正在版本更新中"的问题
"""

import os
import sys
import django

# 添加项目路径
sys.path.append('server/trunk/llol')
sys.path.append('server/trunk/game_lib')

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'llol.settings')
django.setup()

from game_lib.models.main import CacheTable

def fix_maintain_status():
    """修复维护状态"""
    try:
        print("正在检查当前维护状态...")
        
        # 获取当前状态
        full_maintain_status = CacheTable.get('full_maintain_status', 1)
        zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
        
        print(f"当前全服维护状态: {full_maintain_status} (0=维护中, 1=正常)")
        print(f"当前分区维护状态: {zone_maintain_status} (0=维护中, 1=正常)")
        
        # 修复状态
        if full_maintain_status == 0:
            print("检测到全服维护状态为维护中，正在修复...")
            CacheTable.set('full_maintain_status', 1, 3600*24*365*100)
            print("✅ 全服维护状态已修复为正常运行")
        
        if zone_maintain_status == 0:
            print("检测到分区维护状态为维护中，正在修复...")
            CacheTable.set('zone_maintain_status', 1, 3600*24*365*100)
            print("✅ 分区维护状态已修复为正常运行")
        
        # 再次检查
        full_maintain_status = CacheTable.get('full_maintain_status', 1)
        zone_maintain_status = CacheTable.get('zone_maintain_status', 1)
        
        print("\n修复后状态:")
        print(f"全服维护状态: {full_maintain_status}")
        print(f"分区维护状态: {zone_maintain_status}")
        
        if full_maintain_status == 1 and zone_maintain_status == 1:
            print("\n🎉 维护状态修复成功！游戏应该可以正常进入了。")
        else:
            print("\n❌ 修复失败，请检查数据库连接或手动修改。")
            
    except Exception as e:
        print(f"❌ 修复过程中出现错误: {e}")
        print("请检查:")
        print("1. 数据库连接是否正常")
        print("2. Django环境是否正确配置")
        print("3. 缓存服务是否运行")

def show_maintain_info():
    """显示维护状态信息"""
    try:
        maintain_config = CacheTable.get('zone_maintain_config', '{}')
        maintain_config = eval(str(maintain_config))
        
        print("\n维护配置信息:")
        print(f"维护配置: {maintain_config}")
        
        zone_list = maintain_config.get('zone_list', [])
        white_list = maintain_config.get('white_list', [])
        info_msg = maintain_config.get('info_msg', '')
        
        print(f"维护区服列表: {zone_list}")
        print(f"白名单: {white_list}")
        print(f"维护信息: {info_msg}")
        
    except Exception as e:
        print(f"获取维护配置失败: {e}")

if __name__ == "__main__":
    print("=" * 50)
    print("游戏维护状态修复工具")
    print("=" * 50)
    
    # 显示当前状态
    show_maintain_info()
    
    # 修复维护状态
    fix_maintain_status()
    
    print("\n" + "=" * 50)
    print("修复完成！请重新启动游戏客户端测试。")
    print("=" * 50)
