#!/bin/bash
# 智能防护系统 - 基于行为检测而非IP封禁

echo "=========================================="
echo "        智能防护系统部署"
echo "=========================================="

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# 1. 配置系统级防护
configure_system_protection() {
    log "配置系统级防护..."
    
    # 优化TCP参数，防止连接耗尽
    cat > /etc/sysctl.d/99-ddos-protection.conf << 'EOF'
# DDoS防护参数
net.ipv4.tcp_syncookies = 1
net.ipv4.tcp_max_syn_backlog = 2048
net.ipv4.tcp_synack_retries = 2
net.ipv4.tcp_syn_retries = 5
net.ipv4.tcp_fin_timeout = 15
net.ipv4.tcp_keepalive_time = 300
net.ipv4.tcp_keepalive_probes = 5
net.ipv4.tcp_keepalive_intvl = 15
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.core.rmem_max = 16777216
net.core.wmem_max = 16777216
net.core.netdev_max_backlog = 2500
net.core.somaxconn = 65535

# 限制连接数
net.netfilter.nf_conntrack_max = 1000000
net.ipv4.netfilter.ip_conntrack_max = 1000000
EOF
    
    # 应用配置
    sysctl -p /etc/sysctl.d/99-ddos-protection.conf
    log "✅ 系统级防护配置完成"
}

# 2. 配置智能iptables规则
configure_smart_iptables() {
    log "配置智能防火墙规则..."
    
    # 清除现有规则（可选）
    # iptables -F INPUT
    
    # 基础规则
    iptables -A INPUT -i lo -j ACCEPT
    iptables -A INPUT -m state --state ESTABLISHED,RELATED -j ACCEPT
    
    # 限制新连接频率（比IP封禁更有效）
    iptables -A INPUT -p tcp --dport 8001 -m state --state NEW -m recent --set --name game_conn
    iptables -A INPUT -p tcp --dport 8001 -m state --state NEW -m recent --update --seconds 60 --hitcount 20 --name game_conn -j DROP
    
    iptables -A INPUT -p tcp --dport 8004 -m state --state NEW -m recent --set --name game_conn
    iptables -A INPUT -p tcp --dport 8004 -m state --state NEW -m recent --update --seconds 60 --hitcount 20 --name game_conn -j DROP
    
    iptables -A INPUT -p tcp --dport 8005 -m state --state NEW -m recent --set --name game_conn
    iptables -A INPUT -p tcp --dport 8005 -m state --state NEW -m recent --update --seconds 60 --hitcount 20 --name game_conn -j DROP
    
    # 限制HTTP连接频率
    iptables -A INPUT -p tcp --dport 8899 -m state --state NEW -m recent --set --name web_conn
    iptables -A INPUT -p tcp --dport 8899 -m state --state NEW -m recent --update --seconds 60 --hitcount 30 --name web_conn -j DROP
    
    # 限制SSH连接（防止暴力破解）
    iptables -A INPUT -p tcp --dport 22 -m state --state NEW -m recent --set --name ssh_conn
    iptables -A INPUT -p tcp --dport 22 -m state --state NEW -m recent --update --seconds 60 --hitcount 5 --name ssh_conn -j DROP
    
    # 阻止无效包
    iptables -A INPUT -m state --state INVALID -j DROP
    iptables -A INPUT -p tcp --tcp-flags ALL NONE -j DROP
    iptables -A INPUT -p tcp --tcp-flags ALL ALL -j DROP
    
    log "✅ 智能防火墙规则配置完成"
}

# 3. 配置Nginx智能防护
configure_nginx_smart_protection() {
    log "配置Nginx智能防护..."
    
    # 创建Nginx防护配置
    cat > /tmp/nginx_smart_protection.conf << 'EOF'
# Nginx智能防护配置

# 限制请求频率（比IP封禁更有效）
limit_req_zone $binary_remote_addr zone=game_api:10m rate=30r/m;
limit_req_zone $binary_remote_addr zone=game_login:10m rate=5r/m;
limit_req_zone $binary_remote_addr zone=game_general:10m rate=60r/m;

# 限制连接数
limit_conn_zone $binary_remote_addr zone=perip:10m;
limit_conn_zone $server_name zone=perserver:10m;

# 检测攻击模式
map $request_uri $is_attack {
    ~*nmaplowercheck 1;
    ~*evox 1;
    ~*HNAP1 1;
    ~*admin 1;
    ~*phpmyadmin 1;
    ~*wp-admin 1;
    default 0;
}

map $http_user_agent $is_bot_attack {
    ~*nmap 1;
    ~*nikto 1;
    ~*sqlmap 1;
    ~*masscan 1;
    ~*zmap 1;
    "" 1;  # 空User-Agent
    default 0;
}

server {
    # 连接限制
    limit_conn perip 10;
    limit_conn perserver 1000;
    
    # 请求频率限制
    limit_req zone=game_general burst=100 nodelay;
    
    # 阻止攻击请求
    if ($is_attack) {
        return 444;
    }
    
    if ($is_bot_attack) {
        return 444;
    }
    
    # 限制请求大小
    client_max_body_size 10m;
    client_body_buffer_size 128k;
    client_header_buffer_size 3m;
    large_client_header_buffers 4 256k;
    
    # 超时设置
    client_body_timeout 10s;
    client_header_timeout 10s;
    keepalive_timeout 5s 5s;
    send_timeout 10s;
    
    # 特定路径的频率限制
    location /version.json {
        limit_req zone=game_api burst=10 nodelay;
    }
    
    location ~ ^/(login|auth) {
        limit_req zone=game_login burst=3 nodelay;
    }
    
    # 记录可疑访问
    access_log /var/log/nginx/security.log combined if=$is_attack;
}
EOF
    
    log "✅ Nginx智能防护配置已创建: /tmp/nginx_smart_protection.conf"
    log "请手动将配置添加到nginx配置文件中"
}

# 4. 创建智能监控系统
create_smart_monitor() {
    log "创建智能监控系统..."
    
    cat > /root/smart_monitor.sh << 'EOF'
#!/bin/bash
# 智能监控系统 - 基于行为分析

ACCESS_LOG="/www/wwwlogs/127.0.0.1.log"
ALERT_LOG="/root/smart_alerts_$(date +%Y%m%d).log"

log_alert() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$ALERT_LOG"
}

# 检测异常连接模式
detect_abnormal_patterns() {
    local current_minute=$(date '+%d/%b/%Y:%H:%M')
    
    # 检测单IP高频请求
    local high_freq_ips=$(tail -1000 "$ACCESS_LOG" | grep "$current_minute" | awk '{print $1}' | sort | uniq -c | awk '$1 > 50 {print $2}')
    
    for ip in $high_freq_ips; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            # 不直接封禁，而是加入临时限制
            iptables -I INPUT -s "$ip" -m limit --limit 10/min --limit-burst 20 -j ACCEPT
            iptables -I INPUT -s "$ip" -j DROP
            log_alert "🚨 临时限制高频IP: $ip (1小时后自动解除)"
            
            # 1小时后自动解除
            echo "iptables -D INPUT -s $ip -j DROP; iptables -D INPUT -s $ip -m limit --limit 10/min --limit-burst 20 -j ACCEPT" | at now + 1 hour 2>/dev/null
        fi
    done
}

# 检测攻击特征
detect_attack_signatures() {
    local current_minute=$(date '+%d/%b/%Y:%H:%M')
    
    # 检测SSL攻击
    local ssl_attacks=$(tail -200 "$ACCESS_LOG" | grep "$current_minute" | grep '\\x16\\x03' | awk '{print $1}' | sort | uniq)
    
    for ip in $ssl_attacks; do
        if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
            # 临时限制而非永久封禁
            iptables -I INPUT -s "$ip" -p tcp --dport 443 -j DROP
            iptables -I INPUT -s "$ip" -p tcp --dport 8899 -m limit --limit 5/min -j ACCEPT
            log_alert "🚨 检测到SSL攻击，临时限制: $ip"
            
            # 2小时后自动解除
            echo "iptables -D INPUT -s $ip -p tcp --dport 443 -j DROP; iptables -D INPUT -s $ip -p tcp --dport 8899 -m limit --limit 5/min -j ACCEPT" | at now + 2 hours 2>/dev/null
        fi
    done
}

# 系统资源监控
monitor_system_resources() {
    local mem_usage=$(free | grep Mem | awk '{printf("%.1f"), $3/$2 * 100.0}')
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local conn_count=$(ss -tn | wc -l)
    
    if (( $(echo "$mem_usage > 85" | bc -l) )); then
        log_alert "⚠️ 内存使用率过高: ${mem_usage}%"
    fi
    
    if (( $(echo "$load_avg > 4" | bc -l) )); then
        log_alert "⚠️ 系统负载过高: $load_avg"
    fi
    
    if [ "$conn_count" -gt 1000 ]; then
        log_alert "⚠️ 连接数过多: $conn_count"
    fi
}

# 主监控循环
while true; do
    detect_abnormal_patterns
    detect_attack_signatures
    monitor_system_resources
    sleep 60
done
EOF
    
    chmod +x /root/smart_monitor.sh
    log "✅ 智能监控系统已创建: /root/smart_monitor.sh"
}

# 5. 保存配置并设置自启动
save_and_autostart() {
    log "保存配置并设置自启动..."
    
    # 保存iptables规则
    mkdir -p /etc/iptables
    iptables-save > /etc/iptables/rules.v4
    
    # 创建启动脚本
    cat > /etc/systemd/system/game-protection.service << 'EOF'
[Unit]
Description=Game Server Protection Service
After=network.target

[Service]
Type=simple
ExecStart=/root/smart_monitor.sh
Restart=always
RestartSec=10
User=root

[Install]
WantedBy=multi-user.target
EOF
    
    # 启用服务
    systemctl daemon-reload
    systemctl enable game-protection.service
    systemctl start game-protection.service
    
    # 设置iptables自启动
    if ! grep -q "iptables-restore" /etc/rc.local 2>/dev/null; then
        echo "iptables-restore < /etc/iptables/rules.v4" >> /etc/rc.local
        chmod +x /etc/rc.local
    fi
    
    log "✅ 配置已保存，服务已设置为开机自启动"
}

# 6. 生成使用说明
generate_usage_guide() {
    cat > /root/防护系统使用说明.txt << 'EOF'
========================================
        智能防护系统使用说明
========================================

1. 系统特点：
   - 基于行为检测，不依赖IP封禁
   - 自动临时限制，避免误封正常用户
   - 智能监控，自动解除限制
   - 开机自启动，无需手动维护

2. 防护机制：
   - 连接频率限制：防止单IP高频连接
   - 攻击特征检测：自动识别SSL攻击、扫描等
   - 系统资源保护：监控内存、负载、连接数
   - 临时限制策略：1-2小时后自动解除

3. 服务管理：
   启动服务：systemctl start game-protection
   停止服务：systemctl stop game-protection
   查看状态：systemctl status game-protection
   查看日志：tail -f /root/smart_alerts_$(date +%Y%m%d).log

4. 配置文件：
   - 系统参数：/etc/sysctl.d/99-ddos-protection.conf
   - 防火墙规则：/etc/iptables/rules.v4
   - Nginx配置：/tmp/nginx_smart_protection.conf
   - 监控脚本：/root/smart_monitor.sh

5. 日常维护：
   - 无需每次启动游戏时运行脚本
   - 系统会自动处理攻击
   - 定期查看告警日志即可
   - 重启服务器后自动恢复防护

6. 紧急情况：
   如果误封正常用户：
   iptables -L INPUT -n --line-numbers
   iptables -D INPUT <行号>

========================================
EOF
    
    log "✅ 使用说明已生成: /root/防护系统使用说明.txt"
}

# 主执行流程
main() {
    log "开始部署智能防护系统..."
    
    configure_system_protection
    configure_smart_iptables
    configure_nginx_smart_protection
    create_smart_monitor
    save_and_autostart
    generate_usage_guide
    
    echo ""
    echo "=========================================="
    echo "           部署完成报告"
    echo "=========================================="
    echo "✅ 系统级防护：已配置TCP参数优化"
    echo "✅ 智能防火墙：基于频率限制而非IP封禁"
    echo "✅ Nginx防护：请手动应用配置文件"
    echo "✅ 智能监控：已启动后台服务"
    echo "✅ 自动启动：重启后自动恢复防护"
    echo ""
    echo "服务状态："
    systemctl is-active game-protection.service
    echo ""
    echo "使用说明：cat /root/防护系统使用说明.txt"
    echo "=========================================="
    
    log "✅ 智能防护系统部署完成！"
}

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    exit 1
fi

# 执行主程序
main
