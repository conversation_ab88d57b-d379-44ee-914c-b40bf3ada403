#!/bin/bash
# 多进程版游戏服务器性能监控

echo "=========================================="
echo "     多进程游戏服务器性能监控"
echo "=========================================="

# 显示系统基本信息
echo "系统信息："
echo "CPU核心数: $(nproc)"
echo "总内存: $(free -h | grep 'Mem:' | awk '{print $2}')"
echo "当前时间: $(date)"
echo ""

# 显示CPU和内存使用情况
echo "系统资源使用："
top -bn1 | grep "Cpu(s)" | awk '{print "CPU使用率: " $2}' | awk -F'%' '{print $1 "%"}'
free -h | grep "Mem:" | awk '{printf "内存使用: %s / %s (%.1f%%)\n", $3, $2, ($3/$2)*100}'
echo ""

# 显示各服务进程情况
echo "多进程服务状态："
echo "服务名称                    进程数   总CPU%   总内存%"
echo "------------------------------------------------"

# 统计服务进程
analytics_count=$(ps aux | grep "multiprocess_analytics.py\|manage.py.*7701\|manage.py.*7702\|manage.py.*7703" | grep -v grep | wc -l)
analytics_cpu=$(ps aux | grep "multiprocess_analytics.py\|manage.py.*7701\|manage.py.*7702\|manage.py.*7703" | grep -v grep | awk '{sum+=$3} END {printf "%.1f", sum}')
analytics_mem=$(ps aux | grep "multiprocess_analytics.py\|manage.py.*7701\|manage.py.*7702\|manage.py.*7703" | grep -v grep | awk '{sum+=$4} END {printf "%.1f", sum}')
printf "%-30s %-8s %-8s %-8s\n" "统计服务(Analytics)" "$analytics_count" "${analytics_cpu}%" "${analytics_mem}%"

# 后台服务进程
backend_count=$(ps aux | grep "multiprocess_backend.py\|manage.py.*8500\|manage.py.*8501\|manage.py.*8502\|manage.py.*8503" | grep -v grep | wc -l)
backend_cpu=$(ps aux | grep "multiprocess_backend.py\|manage.py.*8500\|manage.py.*8501\|manage.py.*8502\|manage.py.*8503" | grep -v grep | awk '{sum+=$3} END {printf "%.1f", sum}')
backend_mem=$(ps aux | grep "multiprocess_backend.py\|manage.py.*8500\|manage.py.*8501\|manage.py.*8502\|manage.py.*8503" | grep -v grep | awk '{sum+=$4} END {printf "%.1f", sum}')
printf "%-30s %-8s %-8s %-8s\n" "后台服务(Backend)" "$backend_count" "${backend_cpu}%" "${backend_mem}%"

# Node.js战斗服进程
nodejs_count=$(ps aux | grep "LayaMultiProcess.js\|Laya.js.*3001" | grep -v grep | wc -l)
nodejs_cpu=$(ps aux | grep "LayaMultiProcess.js\|Laya.js.*3001" | grep -v grep | awk '{sum+=$3} END {printf "%.1f", sum}')
nodejs_mem=$(ps aux | grep "LayaMultiProcess.js\|Laya.js.*3001" | grep -v grep | awk '{sum+=$4} END {printf "%.1f", sum}')
printf "%-30s %-8s %-8s %-8s\n" "战斗服务(Node.js)" "$nodejs_count" "${nodejs_cpu}%" "${nodejs_mem}%"

# 区服进程
zone_count=$(ps aux | grep "python.*server.py.*zone" | grep -v grep | wc -l)
zone_cpu=$(ps aux | grep "python.*server.py.*zone" | grep -v grep | awk '{sum+=$3} END {printf "%.1f", sum}')
zone_mem=$(ps aux | grep "python.*server.py.*zone" | grep -v grep | awk '{sum+=$4} END {printf "%.1f", sum}')
printf "%-30s %-8s %-8s %-8s\n" "区服服务(Zone)" "$zone_count" "${zone_cpu}%" "${zone_mem}%"

# 备份服务进程
backup_count=$(ps aux | grep "backup_start_sg3.py" | grep -v grep | wc -l)
backup_cpu=$(ps aux | grep "backup_start_sg3.py" | grep -v grep | awk '{sum+=$3} END {printf "%.1f", sum}')
backup_mem=$(ps aux | grep "backup_start_sg3.py" | grep -v grep | awk '{sum+=$4} END {printf "%.1f", sum}')
printf "%-30s %-8s %-8s %-8s\n" "备份服务(Backup)" "$backup_count" "${backup_cpu}%" "${backup_mem}%"

echo ""

# 显示详细进程信息
echo "详细进程信息："
echo "PID     CPU%  内存%  服务类型              命令"
echo "--------------------------------------------------------"

# 统计服务详细信息
ps aux | grep "multiprocess_analytics.py\|manage.py.*770[1-3]" | grep -v grep | awk '{printf "%-8s %-5s %-6s %-20s %s\n", $2, $3"%", $4"%", "统计服务", $11" "$12" "$13}'

# 后台服务详细信息
ps aux | grep "multiprocess_backend.py\|manage.py.*850[0-3]" | grep -v grep | awk '{printf "%-8s %-5s %-6s %-20s %s\n", $2, $3"%", $4"%", "后台服务", $11" "$12" "$13}'

# Node.js服务详细信息
ps aux | grep "LayaMultiProcess.js\|Laya.js.*3001" | grep -v grep | awk '{printf "%-8s %-5s %-6s %-20s %s\n", $2, $3"%", $4"%", "战斗服务", $11" "$12" "$13}'

# 区服详细信息
ps aux | grep "python.*server.py.*zone" | grep -v grep | awk '{printf "%-8s %-5s %-6s %-20s %s\n", $2, $3"%", $4"%", "区服服务", $11" "$12" "$13" "$14}'

echo ""

# 显示网络连接情况
echo "网络连接状态："
total_connections=$(ss -tn | wc -l)
echo "总连接数: $((total_connections-1))"

# 各端口连接数
echo "各端口连接数："
for port in 7701 7702 7703 8500 8501 8502 8503 3001 5001 5002 5003 5011 5012 5013; do
    count=$(ss -tn | grep ":$port " | wc -l)
    if [ $count -gt 0 ]; then
        echo "  端口 $port: $count 个连接"
    fi
done

echo ""

# 显示负载均衡状态
echo "负载均衡检查："
echo "统计服务端口状态："
for port in 7701 7702 7703; do
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo "  ✅ 端口 $port: 运行中"
    else
        echo "  ❌ 端口 $port: 未运行"
    fi
done

echo "后台服务端口状态："
for port in 8500 8501 8502 8503; do
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo "  ✅ 端口 $port: 运行中"
    else
        echo "  ❌ 端口 $port: 未运行"
    fi
done

echo "区服端口状态："
for port in 5001 5002 5003 5011 5012 5013; do
    if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
        echo "  ✅ 端口 $port: 运行中"
    else
        echo "  ❌ 端口 $port: 未运行"
    fi
done

echo ""

# 性能评估
echo "性能评估："
total_processes=$((analytics_count + backend_count + nodejs_count + zone_count + backup_count))
echo "总进程数: $total_processes"

if [ $total_processes -gt 15 ]; then
    echo "✅ 多进程部署成功，进程数量充足"
elif [ $total_processes -gt 8 ]; then
    echo "⚠️  多进程部署部分成功，建议检查未启动的服务"
else
    echo "❌ 多进程部署可能有问题，进程数量过少"
fi

# CPU利用率评估
cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | awk -F'%' '{print $1}')
if (( $(echo "$cpu_usage > 80" | bc -l 2>/dev/null || echo "0") )); then
    echo "⚠️  CPU使用率较高: ${cpu_usage}%"
elif (( $(echo "$cpu_usage > 50" | bc -l 2>/dev/null || echo "0") )); then
    echo "✅ CPU使用率正常: ${cpu_usage}%"
else
    echo "✅ CPU使用率较低: ${cpu_usage}%"
fi

echo ""
echo "建议："
echo "- 如果进程数量不足，请检查启动日志"
echo "- 如果CPU使用率过高，考虑减少进程数量"
echo "- 如果某些端口未运行，检查端口冲突"
echo "- 定期监控确保所有进程正常运行"

echo "=========================================="
