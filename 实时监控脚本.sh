#!/bin/bash
# 实时监控游戏服务器状态

echo "=========================================="
echo "        游戏服务器实时监控"
echo "=========================================="

MONITOR_LOG="server_monitor_$(date +%Y%m%d_%H%M%S).log"
ALERT_LOG="server_alerts_$(date +%Y%m%d_%H%M%S).log"

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$MONITOR_LOG"
}

alert() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] 🚨 ALERT: $1" | tee -a "$ALERT_LOG"
    log "🚨 ALERT: $1"
}

# 检查游戏进程
check_game_processes() {
    local zones=(1 4 5)  # 根据您的区服调整
    
    for zone in "${zones[@]}"; do
        local port=$((8000 + zone))
        local process_count=$(ps aux | grep "python.*$port" | grep -v grep | wc -l)
        
        if [ "$process_count" -eq 0 ]; then
            alert "区服 $zone (端口 $port) 进程不存在！"
        else
            log "区服 $zone (端口 $port) 进程正常 (进程数: $process_count)"
        fi
    done
}

# 检查MySQL连接
check_mysql() {
    if ! mysql -uroot -p -e "SELECT 1;" >/dev/null 2>&1; then
        alert "MySQL连接失败！"
        return 1
    fi
    
    local connections=$(mysql -uroot -p -e "SHOW STATUS LIKE 'Threads_connected';" 2>/dev/null | tail -1 | awk '{print $2}')
    local max_connections=$(mysql -uroot -p -e "SHOW VARIABLES LIKE 'max_connections';" 2>/dev/null | tail -1 | awk '{print $2}')
    
    if [ -n "$connections" ] && [ -n "$max_connections" ]; then
        local usage_percent=$((connections * 100 / max_connections))
        if [ "$usage_percent" -gt 80 ]; then
            alert "MySQL连接数过高: $connections/$max_connections (${usage_percent}%)"
        else
            log "MySQL连接正常: $connections/$max_connections (${usage_percent}%)"
        fi
    fi
}

# 检查系统资源
check_system_resources() {
    # 检查内存
    local mem_usage=$(free | grep Mem | awk '{printf("%.1f"), $3/$2 * 100.0}')
    if (( $(echo "$mem_usage > 85" | bc -l) )); then
        alert "内存使用率过高: ${mem_usage}%"
    fi
    
    # 检查磁盘
    local disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 85 ]; then
        alert "磁盘使用率过高: ${disk_usage}%"
    fi
    
    # 检查负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    if (( $(echo "$load_avg > $cpu_cores" | bc -l) )); then
        alert "系统负载过高: $load_avg (CPU核心数: $cpu_cores)"
    fi
    
    log "系统资源 - 内存: ${mem_usage}%, 磁盘: ${disk_usage}%, 负载: $load_avg"
}

# 检查端口
check_ports() {
    local ports=(8001 8004 8005 3306)  # 根据您的配置调整
    
    for port in "${ports[@]}"; do
        if ! netstat -tlnp | grep ":$port " >/dev/null; then
            alert "端口 $port 未监听！"
        else
            log "端口 $port 正常监听"
        fi
    done
}

# 检查维护状态
check_maintain_status() {
    # 这里需要根据您的实际情况调整路径
    if [ -f "/data/check_maintain.py" ]; then
        python /data/check_maintain.py 2>/dev/null | tee -a "$MONITOR_LOG"
    fi
}

# 主监控循环
monitor_loop() {
    local check_count=0
    
    log "开始实时监控..."
    log "监控日志: $MONITOR_LOG"
    log "告警日志: $ALERT_LOG"
    
    while true; do
        check_count=$((check_count + 1))
        
        log "========== 第 $check_count 次检查 =========="
        
        check_game_processes
        check_mysql
        check_system_resources
        check_ports
        check_maintain_status
        
        # 每10次检查输出一次汇总
        if [ $((check_count % 10)) -eq 0 ]; then
            log "========== 汇总报告 (第 $check_count 次检查) =========="
            log "告警数量: $(wc -l < "$ALERT_LOG" 2>/dev/null || echo 0)"
            log "系统运行时间: $(uptime)"
        fi
        
        sleep 30  # 每30秒检查一次
    done
}

# 创建快速检查脚本
create_quick_check() {
    cat > quick_check.sh << 'EOF'
#!/bin/bash
echo "=== 快速状态检查 ==="
echo "时间: $(date)"
echo "内存: $(free -h | grep Mem)"
echo "磁盘: $(df -h /)"
echo "负载: $(uptime)"
echo "游戏进程:"
ps aux | grep python | grep -E "800[1-5]" | grep -v grep
echo "MySQL状态:"
systemctl status mysql --no-pager -l
echo "端口监听:"
netstat -tlnp | grep -E ":(800[1-5]|3306)"
EOF
    chmod +x quick_check.sh
    log "已创建快速检查脚本: quick_check.sh"
}

# 信号处理
trap 'log "监控被中断"; exit 0' INT TERM

# 开始监控
create_quick_check
monitor_loop
