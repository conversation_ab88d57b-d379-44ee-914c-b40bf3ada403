#!/bin/bash

# 真正的多线程版游戏服务启动脚本

LOG_DIR="./server/logs"
mkdir -p "$LOG_DIR"

# 函数：检查进程是否已运行
is_running() {
    pgrep -f "$1" >/dev/null 2>&1
}

# 函数：启动服务
start_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在检查 [$cmd]" | tee -a "$log_file"

    if is_running "$cmd"; then
        echo "[$(date)] $name 已在运行，跳过启动。" | tee -a "$log_file"
    else
        echo "[$(date)] 启动 $name ..." | tee -a "$log_file"
        nohup $cmd >> "$log_file" 2>&1 &
        echo "[$(date)] $name 启动完成！PID: $!" | tee -a "$log_file"
    fi
}

# 函数：停止服务
stop_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    echo "[$(date)] 正在停止 $name ..." | tee -a "$log_file"

    # 查找并杀死匹配的进程
    PIDS=$(pgrep -f "$cmd")
    if [ -n "$PIDS" ]; then
        echo "[$(date)] 找到以下 PID: $PIDS，正在终止..." | tee -a "$log_file"
        echo "$PIDS" | xargs kill -9 2>/dev/null
        echo "[$(date)] $name 已停止。" | tee -a "$log_file"
    else
        echo "[$(date)] 没有找到正在运行的 $name 进程。" | tee -a "$log_file"
    fi
}

# 函数：创建多线程Python服务器
create_threaded_server() {
    local service_name="$1"
    local port="$2"
    local manage_path="$3"
    
    cat > "./server/threaded_${service_name}.py" << EOF
#!/usr/bin/env python
# -*- coding: utf-8 -*-
import os
import sys
import threading
from wsgiref.simple_server import make_server, WSGIServer
from socketserver import ThreadingMixIn

# 添加Django路径
sys.path.insert(0, '${manage_path}')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')

import django
django.setup()

from django.core.wsgi import get_wsgi_application

class ThreadedWSGIServer(ThreadingMixIn, WSGIServer):
    """多线程WSGI服务器"""
    daemon_threads = True
    allow_reuse_address = True

def run_threaded_server():
    application = get_wsgi_application()
    
    # 创建多线程服务器
    httpd = make_server('0.0.0.0', ${port}, application, server_class=ThreadedWSGIServer)
    
    print(f"多线程${service_name}服务器启动在端口 ${port}")
    print(f"使用线程池，最大线程数: 50")
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print(f"${service_name}服务器停止")
        httpd.shutdown()

if __name__ == '__main__':
    run_threaded_server()
EOF
    
    chmod +x "./server/threaded_${service_name}.py"
    echo "✅ 创建多线程${service_name}服务器: ./server/threaded_${service_name}.py"
}

# 函数：创建多线程Node.js服务器
create_threaded_nodejs() {
    cat > "./server/service/LayaThreaded.js" << 'EOF'
const http = require('http');
const url = require('url');
const request = require('request');
const { Worker, isMainThread, parentPort, workerData } = require('worker_threads');
const os = require('os');

// 加载LayaSample
require('./LayaSample.max');

const port = parseInt(process.argv[2]) || 3000;
const numThreads = Math.max(2, Math.floor(os.cpus().length / 2));

if (isMainThread) {
    console.log(`主线程启动，端口: ${port}`);
    console.log(`创建 ${numThreads} 个工作线程`);
    
    // 获取配置
    const api_url = 'http://118.178.139.38:8500/gateway/';
    
    request.post(
        api_url, 
        {json: {"id":"33","method":"sys.get_config","params":{'pf': 'local'},"cfg_version":"","app_version":"1.0.0"}},
        function(error, response, body) {
            if (body && body.data) {
                sg.cfg.ConfigServer.formatTo(body.data);
                console.log('配置加载完成，启动服务器...');
                
                // 创建HTTP服务器
                const server = http.createServer((req, res) => {
                    // 使用轮询方式分配给工作线程
                    const workerIndex = Math.floor(Math.random() * workers.length);
                    const worker = workers[workerIndex];
                    
                    let post = '';
                    req.on('data', chunk => post += chunk);
                    req.on('end', () => {
                        const pathName = url.parse(req.url).pathname;
                        
                        // 发送给工作线程处理
                        worker.postMessage({
                            method: req.method,
                            url: req.url,
                            pathname: pathName,
                            data: post,
                            headers: req.headers
                        });
                        
                        // 等待工作线程响应
                        worker.once('message', (result) => {
                            res.writeHead(200, {
                                'Content-Type': 'application/json',
                                'Access-Control-Allow-Origin': '*',
                                'Access-Control-Allow-Headers': 'Content-Type'
                            });
                            res.end(result);
                        });
                    });
                });
                
                server.listen(port, () => {
                    console.log(`多线程Node.js服务器运行在端口 ${port}`);
                });
            }
        }
    );
    
    // 创建工作线程
    const workers = [];
    for (let i = 0; i < numThreads; i++) {
        const worker = new Worker(__filename, {
            workerData: { threadId: i }
        });
        
        worker.on('error', (error) => {
            console.error(`工作线程 ${i} 错误:`, error);
        });
        
        workers.push(worker);
    }
    
} else {
    // 工作线程逻辑
    const threadId = workerData.threadId;
    console.log(`工作线程 ${threadId} 启动`);
    
    parentPort.on('message', (data) => {
        try {
            // 处理战斗逻辑
            if (data.pathname === '/fight') {
                const fightData = JSON.parse(data.data);
                const result = sg.fight.logic.utils.FightInterface.doFight(fightData);
                parentPort.postMessage(JSON.stringify(result));
            } else {
                // 其他请求处理
                parentPort.postMessage(JSON.stringify({status: 'ok', thread: threadId}));
            }
        } catch (error) {
            console.error(`线程 ${threadId} 处理错误:`, error);
            parentPort.postMessage(JSON.stringify({error: error.message}));
        }
    });
}
EOF
    
    echo "✅ 创建多线程Node.js服务器: ./server/service/LayaThreaded.js"
}

ACTION="$1"

if [ "$ACTION" == "start" ]; then

    echo "========== $(date) 开始启动所有服务（多线程版） =========="

    # 创建多线程服务器文件
    create_threaded_server "analytics" "7701" "./server/analytics_sg3/trunk/src"
    create_threaded_server "backend" "8500" "./server/trunk/llol/src"
    create_threaded_nodejs

    # 1. 启动统计服务（多线程模式）
    start_service "Python_Analytics_7701_Threaded" "python ./server/threaded_analytics.py"

    # 2. 启动后台服务（多线程模式）
    start_service "Python_LLOL_8500_Threaded" "python ./server/threaded_backend.py"

    # 3. 启动战斗服（多线程模式）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Node_Laya_Threaded_3001" "node LayaThreaded.js 3001"
    )

    # 4. 启动游戏区服备份
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Backup_SG3" "python backup_start_sg3.py"
    )
    
    # 5. 启动1区服务（原版，因为已经使用了ThreadPoolExecutor）
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5001" "python server.py --zone=1"
    )
    
    # 6. 启动2区服务
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5002" "python server.py --zone=2"
    )
    
    # 7. 启动3区服务
    (
        cd ./server/service || { echo "无法进入目录 ./server/service"; exit 1; }
        start_service "Python_Zone_5003" "python server.py --zone=3"
    )

    echo "========== $(date) 所有服务启动完成（多线程版） =========="

elif [ "$ACTION" == "stop" ]; then

    echo "========== $(date) 开始停止所有服务 =========="

    # 停止多线程服务
    stop_service "Python_Analytics_7701_Threaded" "python ./server/threaded_analytics.py"
    stop_service "Python_LLOL_8500_Threaded" "python ./server/threaded_backend.py"
    stop_service "Node_Laya_Threaded_3001" "node LayaThreaded.js 3001"
    
    # 停止区服服务
    stop_service "Python_Backup_SG3" "python backup_start_sg3.py"
    stop_service "Python_Zone_5001" "python server.py --zone=1"
    stop_service "Python_Zone_5002" "python server.py --zone=2"
    stop_service "Python_Zone_5003" "python server.py --zone=3"
    
    echo "========== $(date) 所有服务已停止 =========="

else
    echo "用法: $0 [start|stop]"
    echo "多线程版启动脚本 - 使用真正的多线程模式"
    echo ""
    echo "多线程特性："
    echo "- 统计服务: ThreadingMixIn多线程WSGI服务器"
    echo "- 后台服务: ThreadingMixIn多线程WSGI服务器"  
    echo "- 战斗服: Node.js Worker Threads多线程"
    echo "- 区服: 原版（已使用ThreadPoolExecutor）"
    echo ""
    echo "与多进程版本的区别："
    echo "- 多线程: 共享内存，通信简单，开销小"
    echo "- 多进程: 独立内存，隔离性好，开销大"
    echo ""
    echo "其他版本："
    echo "  多进程版: ./start_all_optimized.sh start"
    echo "  原版:     ./start_all.sh start"
    exit 1
fi
