#!/bin/bash

# 多进程游戏服务启动脚本 (MultiProcess)
# 使用方法: ./mp.sh start|stop|status

LOG_DIR="/data/server/logs"
mkdir -p "$LOG_DIR"

# 启动服务函数
start_service() {
    local name="$1"
    local cmd="$2"
    local log_file="$LOG_DIR/${name}.log"

    if pgrep -f "$cmd" >/dev/null 2>&1; then
        echo "[$name] 已运行，跳过"
    else
        echo "[$name] 启动中..."
        nohup $cmd > "$log_file" 2>&1 &
        local pid=$!
        sleep 1
        if kill -0 $pid 2>/dev/null; then
            echo "[$name] 启动成功 PID: $pid"
        else
            echo "[$name] 启动失败，检查日志: $log_file"
        fi
    fi
}

# 停止服务函数
stop_service() {
    local name="$1"
    local cmd="$2"
    
    PIDS=$(pgrep -f "$cmd")
    if [ -n "$PIDS" ]; then
        echo "[$name] 停止中..."
        echo "$PIDS" | xargs kill -9 2>/dev/null
        echo "[$name] 已停止"
    else
        echo "[$name] 未运行"
    fi
}

# 检查服务状态
check_status() {
    echo "========== 服务状态 =========="
    
    # 统计服务 (3个进程)
    analytics_count=$(ps aux | grep "python.*manage.py.*runserver.*770[1-3]" | grep -v grep | wc -l)
    echo "统计服务: $analytics_count/3 个进程"
    
    # 后台服务 (4个进程)
    backend_count=$(ps aux | grep "python.*manage.py.*runserver.*850[0-3]" | grep -v grep | wc -l)
    echo "后台服务: $backend_count/4 个进程"
    
    # 战斗服务
    fight_count=$(ps aux | grep "node.*Laya" | grep -v grep | wc -l)
    echo "战斗服务: $fight_count 个进程"

    # 区服服务
    zone_count=$(ps aux | grep "python.*server.py.*--zone" | grep -v grep | wc -l)
    echo "区服服务: $zone_count 个进程"
    
    # 端口检查
    echo ""
    echo "端口状态:"
    for port in 7701 7702 7703 8500 8501 8502 8503 3001 5001 5002 5003 5004 5005; do
        if netstat -tlnp 2>/dev/null | grep ":$port " >/dev/null; then
            echo "  ✅ $port"
        else
            echo "  ❌ $port"
        fi
    done
}

ACTION="$1"

if [ "$ACTION" == "start" ]; then
    echo "========== 启动多进程服务 =========="
    
    # 1. 统计服务 (3个进程)
    start_service "Analytics_7701" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701"
    start_service "Analytics_7702" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7702"
    start_service "Analytics_7703" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7703"

    # 2. 后台服务 (4个进程)
    start_service "Backend_8500" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8500"
    start_service "Backend_8501" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8501"
    start_service "Backend_8502" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8502"
    start_service "Backend_8503" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8503"

    # 3. 战斗服务 (直接启动)
    (
        cd ./server/service || exit 1
        start_service "Fight_3001" "node Laya.js"
    )

    # 4. 区服服务
    (
        cd ./server/service || exit 1
        start_service "Zone_5001" "python server.py --zone=1"
        start_service "Zone_5002" "python server.py --zone=2"
        start_service "Zone_5003" "python server.py --zone=3"
        start_service "Zone_5004" "python server.py --zone=4"
        start_service "Zone_5005" "python server.py --zone=5"
    )

    # 5. 备份服务
    (
        cd ./server/service || exit 1
        start_service "Backup_SG3" "python backup_start_sg3.py"
    )

    echo "========== 多进程启动完成 =========="
    sleep 3
    check_status

elif [ "$ACTION" == "stop" ]; then
    echo "========== 停止多进程服务 =========="
    
    # 停止统计服务
    stop_service "Analytics_7701" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7701"
    stop_service "Analytics_7702" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7702"
    stop_service "Analytics_7703" "python ./server/analytics_sg3/trunk/src/manage.py runserver 0.0.0.0:7703"

    # 停止后台服务
    stop_service "Backend_8500" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8500"
    stop_service "Backend_8501" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8501"
    stop_service "Backend_8502" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8502"
    stop_service "Backend_8503" "python ./server/trunk/llol/src/manage.py runserver 0.0.0.0:8503"

    # 停止战斗服务
    stop_service "Fight_3001" "node Laya.js"

    # 停止区服服务
    stop_service "Zone_5001" "python server.py --zone=1"
    stop_service "Zone_5002" "python server.py --zone=2"
    stop_service "Zone_5003" "python server.py --zone=3"
    stop_service "Zone_5004" "python server.py --zone=4"
    stop_service "Zone_5005" "python server.py --zone=5"

    # 停止备份服务
    stop_service "Backup_SG3" "python backup_start_sg3.py"
    
    echo "========== 多进程停止完成 =========="

elif [ "$ACTION" == "status" ]; then
    check_status

else
    echo "多进程游戏服务管理脚本"
    echo ""
    echo "用法: ./mp.sh [start|stop|status]"
    echo ""
    echo "  start   - 启动所有多进程服务"
    echo "  stop    - 停止所有多进程服务"
    echo "  status  - 查看服务状态"
    echo ""
    echo "多进程架构:"
    echo "  统计服务: 3个进程 (7701-7703)"
    echo "  后台服务: 4个进程 (8500-8503)"
    echo "  战斗服务: Node.js cluster (3001)"
    echo "  区服服务: 5个进程 (5001-5005)"
    echo "  备份服务: 1个进程"
    echo ""
    echo "总计: 约13-16个进程"
fi
