#!/bin/bash
# 防火墙管理工具 - 简单易用的IP封禁管理

RULES_FILE="/etc/iptables/rules.v4"

show_menu() {
    echo "=========================================="
    echo "           防火墙管理工具"
    echo "=========================================="
    echo "1. 封禁IP地址"
    echo "2. 解封IP地址"
    echo "3. 查看封禁列表"
    echo "4. 查看攻击日志"
    echo "5. 保存规则(永久生效)"
    echo "6. 恢复规则"
    echo "7. 清空所有封禁"
    echo "8. 自动封禁攻击IP"
    echo "0. 退出"
    echo "=========================================="
}

# 封禁IP
block_ip() {
    echo -n "请输入要封禁的IP地址: "
    read ip
    
    if [[ ! $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "❌ IP地址格式错误！"
        return
    fi
    
    # 检查是否已经封禁
    if iptables -L INPUT -n | grep -q "$ip"; then
        echo "⚠️  IP $ip 已经被封禁"
        return
    fi
    
    iptables -A INPUT -s "$ip" -j DROP
    echo "✅ 已封禁IP: $ip"
}

# 解封IP
unblock_ip() {
    echo -n "请输入要解封的IP地址: "
    read ip
    
    if [[ ! $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
        echo "❌ IP地址格式错误！"
        return
    fi
    
    iptables -D INPUT -s "$ip" -j DROP 2>/dev/null
    if [ $? -eq 0 ]; then
        echo "✅ 已解封IP: $ip"
    else
        echo "⚠️  IP $ip 未被封禁或解封失败"
    fi
}

# 查看封禁列表
show_blocked() {
    echo "当前封禁的IP列表:"
    echo "----------------------------------------"
    local blocked_ips=$(iptables -L INPUT -n | grep DROP | awk '{print $4}' | grep -v "0.0.0.0/0")
    
    if [ -z "$blocked_ips" ]; then
        echo "暂无封禁的IP"
    else
        echo "$blocked_ips" | nl
    fi
    echo "----------------------------------------"
    echo "总计: $(echo "$blocked_ips" | wc -l) 个IP被封禁"
}

# 查看攻击日志
show_attack_logs() {
    echo "最近的攻击日志:"
    echo "----------------------------------------"
    if [ -f /var/log/nginx/error.log ]; then
        grep -E "nmaplowercheck|evox|HNAP1|sdk" /var/log/nginx/error.log | tail -20
    else
        echo "未找到nginx错误日志"
    fi
}

# 保存规则
save_rules() {
    mkdir -p /etc/iptables
    iptables-save > "$RULES_FILE"
    
    # 设置开机自动加载
    if ! grep -q "iptables-restore" /etc/rc.local 2>/dev/null; then
        echo "iptables-restore < $RULES_FILE" >> /etc/rc.local
        chmod +x /etc/rc.local
    fi
    
    echo "✅ 防火墙规则已保存，重启后自动生效"
}

# 恢复规则
restore_rules() {
    if [ -f "$RULES_FILE" ]; then
        iptables-restore < "$RULES_FILE"
        echo "✅ 防火墙规则已恢复"
    else
        echo "❌ 未找到保存的规则文件"
    fi
}

# 清空所有封禁
clear_all() {
    echo -n "确定要清空所有封禁规则吗？(y/N): "
    read confirm
    
    if [[ $confirm =~ ^[Yy]$ ]]; then
        # 删除所有DROP规则
        iptables -L INPUT --line-numbers -n | grep DROP | awk '{print $1}' | sort -nr | while read line; do
            iptables -D INPUT $line
        done
        echo "✅ 已清空所有封禁规则"
    else
        echo "操作已取消"
    fi
}

# 自动封禁攻击IP
auto_block_attacks() {
    echo "正在扫描攻击IP..."
    
    local attack_count=0
    if [ -f /var/log/nginx/error.log ]; then
        # 获取攻击IP列表
        local attack_ips=$(grep -E "nmaplowercheck|evox|HNAP1|sdk" /var/log/nginx/error.log | \
                          awk '{print $12}' | sed 's/client://' | sed 's/,//' | \
                          sort | uniq)
        
        for ip in $attack_ips; do
            if [[ $ip =~ ^[0-9]+\.[0-9]+\.[0-9]+\.[0-9]+$ ]]; then
                # 检查是否已经封禁
                if ! iptables -L INPUT -n | grep -q "$ip"; then
                    iptables -A INPUT -s "$ip" -j DROP
                    echo "🚨 自动封禁攻击IP: $ip"
                    ((attack_count++))
                fi
            fi
        done
    fi
    
    echo "✅ 自动封禁完成，共封禁 $attack_count 个攻击IP"
}

# 主程序
main() {
    while true; do
        show_menu
        echo -n "请选择操作 (0-8): "
        read choice
        
        case $choice in
            1) block_ip ;;
            2) unblock_ip ;;
            3) show_blocked ;;
            4) show_attack_logs ;;
            5) save_rules ;;
            6) restore_rules ;;
            7) clear_all ;;
            8) auto_block_attacks ;;
            0) echo "再见！"; exit 0 ;;
            *) echo "❌ 无效选择，请重新输入" ;;
        esac
        
        echo
        echo -n "按回车键继续..."
        read
        clear
    done
}

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "❌ 请使用root权限运行此脚本"
    exit 1
fi

# 启动程序
clear
main
